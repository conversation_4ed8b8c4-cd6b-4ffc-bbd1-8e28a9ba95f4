<?php

/**
 * 用结构名词3.xlsx补充数据库空字段脚本
 * 通过KEY对比，补充noun_en和note_en字段的空值
 */

// 设置脚本执行时间限制
set_time_limit(0);
ini_set('memory_limit', '512M');

// 引入Laravel框架
require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\NounStructure;
use App\Services\Common\OfficeService;

echo "=== 用结构名词3.xlsx补充数据库空字段 ===\n";
echo "开始时间: " . date('Y-m-d H:i:s') . "\n\n";

// 检查文件
$file3Path = storage_path('app/public/excel/结构名词3.xlsx');

if (!file_exists($file3Path)) {
    echo "❌ 结构名词3.xlsx 文件不存在\n";
    exit(1);
}

echo "=== 第一步：读取结构名词3.xlsx ===\n";

try {
    $sheet3 = OfficeService::importExecl($file3Path);
    $totalRows = count($sheet3);
    echo "✅ 文件读取成功，总行数: {$totalRows}\n";
    echo "数据行数（不含表头）: " . ($totalRows - 1) . "\n\n";
} catch (Exception $e) {
    echo "❌ 文件读取失败: " . $e->getMessage() . "\n";
    exit(1);
}

echo "=== 第二步：提取Excel3中的数据 ===\n";

$excel3Data = [];
$validCount = 0;
$skippedCount = 0;

foreach ($sheet3 as $rowIndex => $row) {
    // 跳过表头行
    if ($rowIndex <= 1) {
        continue;
    }

    // 检查第二列（B列）是否存在且不为空
    if (!isset($row['B']) || trim($row['B']) === '') {
        $skippedCount++;
        continue;
    }

    $key = trim($row['B']);
    $nounEn = isset($row['E']) ? trim($row['E']) : '';  // 第五列
    $noteEn = isset($row['F']) ? trim($row['F']) : '';  // 第六列

    // 存储数据
    $excel3Data[$key] = [
        'row' => $rowIndex,
        'key' => $key,
        'noun_en' => $nounEn,
        'note_en' => $noteEn
    ];
    
    $validCount++;
    
    // 显示进度
    if ($validCount % 500 == 0) {
        echo "读取进度: {$validCount} 条数据\n";
    }
}

echo "Excel3中有效数据: {$validCount} 条\n";
echo "Excel3中跳过数据: {$skippedCount} 条\n";
echo "Excel3中唯一KEY数: " . count($excel3Data) . "\n\n";

echo "=== 第三步：检查数据库中的空字段 ===\n";

// 查找数据库中noun_en或note_en为空的记录
$emptyRecords = NounStructure::where(function($query) {
    $query->whereNull('noun_en')
          ->orWhere('noun_en', '')
          ->orWhereNull('note_en')
          ->orWhere('note_en', '');
})->get();

$emptyCount = $emptyRecords->count();
echo "数据库中有空字段的记录数: {$emptyCount}\n\n";

if ($emptyCount == 0) {
    echo "✅ 数据库中没有空字段需要补充\n";
    exit(0);
}

echo "=== 第四步：开始补充空字段 ===\n";

$matchedCount = 0;      // 在Excel3中找到匹配KEY的数量
$updatedCount = 0;      // 实际更新的记录数量
$nounEnUpdated = 0;     // noun_en字段更新数量
$noteEnUpdated = 0;     // note_en字段更新数量
$notFoundCount = 0;     // 在Excel3中未找到的数量
$noUpdateNeeded = 0;    // 不需要更新的数量

foreach ($emptyRecords as $record) {
    $key = $record->key;
    
    // 显示进度
    if (($matchedCount + $notFoundCount) % 100 == 0) {
        $processed = $matchedCount + $notFoundCount;
        $percentage = round(($processed / $emptyCount) * 100, 2);
        echo "处理进度: {$processed}/{$emptyCount} ({$percentage}%) - 匹配: {$matchedCount}, 更新: {$updatedCount}\n";
    }
    
    // 在Excel3中查找对应的KEY
    if (!isset($excel3Data[$key])) {
        $notFoundCount++;
        continue;
    }
    
    $matchedCount++;
    $excel3Record = $excel3Data[$key];
    
    // 检查哪些字段需要更新
    $updateData = [];
    $updatedFields = [];
    
    // 检查noun_en字段
    if ((is_null($record->noun_en) || $record->noun_en === '') && !empty($excel3Record['noun_en'])) {
        $updateData['noun_en'] = $excel3Record['noun_en'];
        $updatedFields[] = 'noun_en';
        $nounEnUpdated++;
    }
    
    // 检查note_en字段
    if ((is_null($record->note_en) || $record->note_en === '') && !empty($excel3Record['note_en'])) {
        $updateData['note_en'] = $excel3Record['note_en'];
        $updatedFields[] = 'note_en';
        $noteEnUpdated++;
    }
    
    // 如果有需要更新的字段
    if (!empty($updateData)) {
        try {
            $record->update($updateData);
            $updatedCount++;
            
            echo "✅ 更新KEY: {$key} (Excel3行 {$excel3Record['row']})\n";
            echo "   更新字段: " . implode(', ', $updatedFields) . "\n";
            
            // 显示更新的内容（截断显示）
            foreach ($updatedFields as $field) {
                $value = $updateData[$field];
                $displayValue = mb_strlen($value) > 50 ? mb_substr($value, 0, 50) . '...' : $value;
                echo "   {$field}: \"{$displayValue}\"\n";
            }
            echo "   ---\n";
            
        } catch (Exception $e) {
            echo "❌ 更新失败KEY: {$key}\n";
            echo "   错误: " . $e->getMessage() . "\n";
            
            // 如果是数据过长错误，尝试截断
            if (strpos($e->getMessage(), 'Data too long') !== false) {
                echo "   尝试截断数据...\n";
                try {
                    $truncatedData = [];
                    foreach ($updateData as $field => $value) {
                        if ($field === 'noun_en') {
                            $truncatedData[$field] = mb_substr($value, 0, 1000);
                        } elseif ($field === 'note_en') {
                            $truncatedData[$field] = mb_substr($value, 0, 10000);
                        } else {
                            $truncatedData[$field] = $value;
                        }
                    }
                    
                    $record->update($truncatedData);
                    $updatedCount++;
                    echo "   ✅ 截断后更新成功\n";
                    
                } catch (Exception $e2) {
                    echo "   ❌ 截断后仍然失败: " . $e2->getMessage() . "\n";
                }
            }
        }
    } else {
        $noUpdateNeeded++;
    }
}

echo "\n=== 第五步：补充结果统计 ===\n";
echo "数据库中有空字段的记录: {$emptyCount}\n";
echo "在Excel3中找到匹配KEY: {$matchedCount}\n";
echo "在Excel3中未找到KEY: {$notFoundCount}\n";
echo "实际更新的记录数: {$updatedCount}\n";
echo "不需要更新的记录: {$noUpdateNeeded}\n";
echo "noun_en字段更新数: {$nounEnUpdated}\n";
echo "note_en字段更新数: {$noteEnUpdated}\n";

echo "\n=== 第六步：最终验证 ===\n";

// 重新检查空字段数量
$remainingEmpty = NounStructure::where(function($query) {
    $query->whereNull('noun_en')
          ->orWhere('noun_en', '')
          ->orWhereNull('note_en')
          ->orWhere('note_en', '');
})->count();

echo "补充前空字段记录数: {$emptyCount}\n";
echo "补充后空字段记录数: {$remainingEmpty}\n";
echo "减少空字段记录数: " . ($emptyCount - $remainingEmpty) . "\n";

// 统计完整记录数
$completeRecords = NounStructure::whereNotNull('noun_ch')
                                ->whereNotNull('note_ch')
                                ->whereNotNull('noun_en')
                                ->whereNotNull('note_en')
                                ->where('noun_ch', '!=', '')
                                ->where('note_ch', '!=', '')
                                ->where('noun_en', '!=', '')
                                ->where('note_en', '!=', '')
                                ->count();

$totalRecords = NounStructure::count();
$completionRate = $totalRecords > 0 ? round(($completeRecords / $totalRecords) * 100, 2) : 0;

echo "\n总记录数: {$totalRecords}\n";
echo "完整记录数: {$completeRecords}\n";
echo "完整率: {$completionRate}%\n";

if ($remainingEmpty == 0) {
    echo "\n🎉 完美！所有字段都已补充完整\n";
} else {
    echo "\n⚠️  仍有 {$remainingEmpty} 条记录存在空字段\n";
    echo "可能原因：Excel3中没有对应的数据或数据为空\n";
}

echo "\n=== 补充完成 ===\n";
echo "结束时间: " . date('Y-m-d H:i:s') . "\n";
