<?php

/**
 * 检查空字段脚本
 * 查看数据库中哪些字段为空，需要补充
 */

// 引入Laravel框架
require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\NounStructure;

echo "=== 检查数据库空字段状态 ===\n";
echo "检查时间: " . date('Y-m-d H:i:s') . "\n\n";

echo "=== 总体统计 ===\n";

$totalRecords = NounStructure::count();
echo "总记录数: {$totalRecords}\n";

// 各字段空值统计
$emptyNounCh = NounStructure::where(function($query) {
    $query->whereNull('noun_ch')->orWhere('noun_ch', '');
})->count();

$emptyNoteCh = NounStructure::where(function($query) {
    $query->whereNull('note_ch')->orWhere('note_ch', '');
})->count();

$emptyNounEn = NounStructure::where(function($query) {
    $query->whereNull('noun_en')->orWhere('noun_en', '');
})->count();

$emptyNoteEn = NounStructure::where(function($query) {
    $query->whereNull('note_en')->orWhere('note_en', '');
})->count();

echo "noun_ch 空值记录数: {$emptyNounCh}\n";
echo "note_ch 空值记录数: {$emptyNoteCh}\n";
echo "noun_en 空值记录数: {$emptyNounEn}\n";
echo "note_en 空值记录数: {$emptyNoteEn}\n";

// 完整记录统计
$completeRecords = NounStructure::whereNotNull('noun_ch')
                                ->whereNotNull('note_ch')
                                ->whereNotNull('noun_en')
                                ->whereNotNull('note_en')
                                ->where('noun_ch', '!=', '')
                                ->where('note_ch', '!=', '')
                                ->where('noun_en', '!=', '')
                                ->where('note_en', '!=', '')
                                ->count();

$completionRate = $totalRecords > 0 ? round(($completeRecords / $totalRecords) * 100, 2) : 0;

echo "\n完整记录数（所有字段都有值）: {$completeRecords}\n";
echo "完整率: {$completionRate}%\n";

// 需要补充的记录（noun_en或note_en为空）
$needSupplement = NounStructure::where(function($query) {
    $query->whereNull('noun_en')
          ->orWhere('noun_en', '')
          ->orWhereNull('note_en')
          ->orWhere('note_en', '');
})->count();

echo "\n需要补充英文字段的记录数: {$needSupplement}\n";

if ($needSupplement > 0) {
    echo "\n=== 需要补充的记录示例（前10条） ===\n";
    
    $sampleRecords = NounStructure::where(function($query) {
        $query->whereNull('noun_en')
              ->orWhere('noun_en', '')
              ->orWhereNull('note_en')
              ->orWhere('note_en', '');
    })->take(10)->get();
    
    foreach ($sampleRecords as $index => $record) {
        $num = $index + 1;
        echo "{$num}. KEY: {$record->key}\n";
        echo "   noun_ch: " . ($record->noun_ch ?: '[空]') . "\n";
        echo "   note_ch: " . ($record->note_ch ?: '[空]') . "\n";
        echo "   noun_en: " . ($record->noun_en ?: '[空]') . "\n";
        echo "   note_en: " . ($record->note_en ?: '[空]') . "\n";
        echo "   ---\n";
    }
    
    if ($needSupplement > 10) {
        echo "... 还有 " . ($needSupplement - 10) . " 条记录需要补充\n";
    }
    
    echo "\n💡 建议运行: php supplement_from_excel3.php 来补充这些空字段\n";
} else {
    echo "\n✅ 所有记录的英文字段都已完整\n";
}

echo "\n=== 检查完成 ===\n";
