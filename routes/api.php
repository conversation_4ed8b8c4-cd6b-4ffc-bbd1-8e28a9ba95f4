<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
Route::post('login', [\App\Http\Controllers\AuthController::class, 'login']);

//Route::middleware('auth:api')->get('/user', function (Request $request) {
Route::middleware('jwt.auth')->group(function () {
    Route::post('logout', [\App\Http\Controllers\AuthController::class, 'logout']);
    Route::post('category/index', [\App\Http\Controllers\CategoryController::class, 'index']);
    Route::post('post/index', [\App\Http\Controllers\PostController::class, 'index']);
    Route::post('loginRecords/records', [\App\Http\Controllers\LoginRecordsController::class, 'records']);
    Route::post('loginRecords/refresh', [\App\Http\Controllers\LoginRecordsController::class, 'refresh']);
    Route::post('updatePassword', [\App\Http\Controllers\AuthController::class, 'updatePassword']);

    // 收藏功能路由
    Route::post('collect/folder/create', [\App\Http\Controllers\CollectController::class, 'createFolder']);
    Route::post('collect/create', [\App\Http\Controllers\CollectController::class, 'createCollect']);
    Route::post('collect/cancel', [\App\Http\Controllers\CollectController::class, 'cancelCollect']);
    Route::post('collect/list', [\App\Http\Controllers\CollectController::class, 'collectList']);
    Route::post('collect/check', [\App\Http\Controllers\CollectController::class, 'checkCollect']);
    Route::post('collect/stats', [\App\Http\Controllers\CollectController::class, 'collectStats']);
});
Route::post('nounStructure/list', [\App\Http\Controllers\NounStructureController::class, 'index']);




