<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
Route::post('user/login', [\App\Http\Controllers\Admin\UserController::class, 'login']);




//Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
Route::middleware('admin.jwt')->group(function () {
    Route::post('user/index', [\App\Http\Controllers\Admin\UserController::class, 'index']);
    Route::post('user/upload', [\App\Http\Controllers\Admin\UserController::class, 'import']);
    Route::post('category/index', [\App\Http\Controllers\Admin\CategoryController::class, 'index']);
    Route::post('category/store', [\App\Http\Controllers\Admin\CategoryController::class, 'store']);
    Route::post('category/show', [\App\Http\Controllers\Admin\CategoryController::class, 'show']);
    Route::post('category/update', [\App\Http\Controllers\Admin\CategoryController::class, 'update']);
    Route::post('category/destroy', [\App\Http\Controllers\Admin\CategoryController::class, 'destroy']);
    Route::post('post/importExcel', [\App\Http\Controllers\Admin\PostController::class, 'importExcel']);
    Route::post('post/index', [\App\Http\Controllers\Admin\PostController::class, 'index']);
    Route::post('post/updateSort', [\App\Http\Controllers\Admin\PostController::class, 'updateSort']);
    // 学校管理路由
    Route::post('school/index', [\App\Http\Controllers\Admin\SchoolController::class, 'index']);
    Route::post('school/store', [\App\Http\Controllers\Admin\SchoolController::class, 'store']);
    Route::post('school/show', [\App\Http\Controllers\Admin\SchoolController::class, 'show']);
    Route::post('school/update', [\App\Http\Controllers\Admin\SchoolController::class, 'update']);
    Route::post('school/destroy', [\App\Http\Controllers\Admin\SchoolController::class, 'destroy']);
    // 批量更新学校学生有效期
    Route::post('school/updateExpireTime', [\App\Http\Controllers\Admin\SchoolController::class, 'updateExpireTime']);


});
// 结构名词管理
Route::post('noun-structure/import', [\App\Http\Controllers\Admin\NounStructureController::class, 'importNounStructure']);
Route::post('noun-structure/index', [\App\Http\Controllers\Admin\NounStructureController::class, 'index']);
Route::post('noun-structure/stats', [\App\Http\Controllers\Admin\NounStructureController::class, 'stats']);




