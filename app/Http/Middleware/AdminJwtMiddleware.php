<?php

namespace App\Http\Middleware;

use Closure;
use Exception;
use Tymon\JWTAuth\Facades\JWTAuth;
use <PERSON><PERSON>\JWTAuth\Http\Middleware\BaseMiddleware;
use App\Models\AdminUser;

class AdminJwtMiddleware extends BaseMiddleware
{
    public function handle($request, Closure $next)
    {
        try {
            // 检查令牌是否存在
            $token = JWTAuth::getToken();
            if (!$token) {
                return response()->json(['code' => 401, 'message' => '令牌不存在'], 401);
            }

            // 解析令牌
            $payload = JWTAuth::getPayload($token)->toArray();

            // 检查是否包含必要的信息
            if (!isset($payload['username']) || $payload['username'] !== AdminUser::getAdminUser()->username) {
                return response()->json(['code' => 401, 'message' => '无效的用户令牌'], 401);
            }

        } catch (Exception $e) {
            if ($e instanceof \Tymon\JWTAuth\Exceptions\TokenInvalidException) {
                return response()->json(['code' => 401, 'message' => '令牌无效'], 401);
            } else if ($e instanceof \Tymon\JWTAuth\Exceptions\TokenExpiredException) {
                return response()->json(['code' => 401, 'message' => '令牌已过期'], 401);
            } else {
                return response()->json(['code' => 401, 'message' => '认证失败: ' . $e->getMessage()], 401);
            }
        }

        return $next($request);
    }
}
