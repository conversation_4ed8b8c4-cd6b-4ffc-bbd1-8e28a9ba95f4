<?php

namespace App\Http\Middleware;

use Closure;
use Exception;
use Tymon\JWTAuth\Facades\JWTAuth;
use <PERSON>mon\JWTAuth\Http\Middleware\BaseMiddleware;
use App\Models\User;

class JwtAuthMiddleware extends BaseMiddleware
{
    public function handle($request, Closure $next)
    {
        try {
            // 检查令牌是否存在
            $token = JWTAuth::getToken();
            if (!$token) {
                return response()->json(['code' => 401, 'message' => '令牌不存在'], 401);
            }

            // 解析令牌并获取用户
            $user = JWTAuth::parseToken()->authenticate();
            if (!$user) {
                return response()->json(['code' => 401, 'message' => '用户不存在'], 401);
            }

            // 检查用户状态
            if ($user->status !== 1) {
                return response()->json(['code' => 403, 'message' => '账号已被禁用'], 403);
            }

            // 检查账号是否过期
            if ($user->expire_time && now()->gt($user->expire_time)) {
                return response()->json(['code' => 403, 'message' => '账号已过期'], 403);
            }

            // 将用户信息添加到请求中
            $request->setUserResolver(function () use ($user) {
                return $user;
            });

        } catch (Exception $e) {
            if ($e instanceof \Tymon\JWTAuth\Exceptions\TokenInvalidException) {
                return response()->json(['code' => 401, 'message' => '令牌无效'], 401);
            } else if ($e instanceof \Tymon\JWTAuth\Exceptions\TokenExpiredException) {
                return response()->json(['code' => 401, 'message' => '令牌已过期'], 401);
            } else {
                return response()->json(['code' => 401, 'message' => '认证失败: ' . $e->getMessage()], 401);
            }
        }

        return $next($request);
    }
}
