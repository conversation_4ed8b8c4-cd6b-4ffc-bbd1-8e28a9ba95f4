<?php

namespace App\Http\Controllers;

use App\Models\LoginRecords;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class LoginRecordsController extends Controller
{
    /**
     * 记录登录信息
     * @param Request $request
     * @return array
     */
    public function records(Request $request)
    {
        // 验证输入
        $validator = Validator::make($request->all(), [
            'code' => ['required', 'string'],
        ]);
        if ($validator->fails()) {
            return ['code' => 3001, 'message' => $validator->errors()->first(), 'data' => $validator->errors()];
        }
        // 获取当前在线人数
        $onlineCount = LoginRecords::where('status', 1)->count();
        $list = LoginRecords::where('code',$request->code)->first();
        // 如果在线人数超过 50，返回错误
        if ($onlineCount >= 50 && $list['status'] == 0) {
            return ['code' => 4001, 'message' => 'The current number of online users exceeds 50, and login is temporarily unavailable.',];
        }
        // 获取请求中的 code
        $code = $request->input('code');
        $currentTime = date('Y-m-d H:i:s');
        $record = LoginRecords::where('code', $code)->first();
        if ($record) {
            // 如果记录存在，仅更新时间和状态
            $updated = $record->update([
                'status' => 1,
                'login_time' => $currentTime,
                'updated_at' => $currentTime,
            ]);
            return $updated
                ? ['code' => 200, 'message' => 'The login status is updated successfully']
                : ['code' => 5001, 'message' => 'Failed to update login status'];
        } else {
            // 新用户，检查在线人数
            $onlineCount = LoginRecords::where('status', 1)->count();
            if ($onlineCount >= 50) {
                return ['code' => 4001, 'message' => 'The current number of online users exceeds 50, and login is temporarily unavailable.'];
            }

            // 在线人数未超过限制，创建新记录
            $data = [
                'code' => $code,
                'status' => 1,
                'login_time' => $currentTime,
                'created_at' => $currentTime,
                'updated_at' => $currentTime,
            ];
            $rows = LoginRecords::create($data);
            return $rows
                ? ['code' => 200, 'message' => 'Login successful']
                : ['code' => 5001, 'message' => 'Login failure'];
        }
    }

    /**
     * 刷新登录信息
     * @param Request $request
     * @return array|void
     */
    public function refresh(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'code' => ['required', 'string'],
        ]);
        if ($validator->fails()) {
            return ['code' => 3001, 'message' => $validator->errors()->first(), 'data' => $validator->errors()];
        }

        $code = $request->input('code');
        $currentTime = date('Y-m-d H:i:s');

        $record = LoginRecords::where('code', $code)->first();
        if ($record) {
            $updated = $record->update(['status' => 1, 'login_time' => $currentTime]);
            if ($updated) {
                return ['code' => 200, 'message' => 'Refresh successfully'];
            } else {
                return ['code' => 5001, 'message' => 'Refresh failure'];
            }
        }
    }

}

