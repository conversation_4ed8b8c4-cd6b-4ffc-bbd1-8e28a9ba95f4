<?php

namespace App\Http\Controllers;

use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CategoryController extends Controller
{
    public function index(Request $request)
    {
        $validator = Validator::make($request->all(),[
            'category_code' => 'required|string',
        ]);
        if ($validator->fails()) {
            return ['code' => 3001, 'message' => $validator->errors()->first(), 'data' => $validator->errors()];
        }
        $categoryCode = Category::query()->where('code',$request->category_code)->first();
        $id = $categoryCode['id'];
        $categories = Category::with('children')
            ->whereNull('parent_id')
            ->where('status',1)
            ->where('id',$id)
            ->get();
        return ['code'=>200,'data'=>$categories];
    }
}

