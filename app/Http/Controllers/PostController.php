<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Collect;
use App\Models\Post;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class PostController extends Controller
{
    public function index(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'category_code' => 'required|string',
        ]);
        if ($validator->fails()) {
            return ['code' => 422, 'message' => $validator->errors()];
        }

        // 获取当前分类
        $categoryList = Category::query()->where('code', $request->category_code)->first();
        if (!$categoryList) {
            return ['code' => 404, 'message' => 'Category not found'];
        }

        // 处理分类代码
        if (!$categoryList['parent_id']) {
            // 如果是父类，获取所有子类的 code
            $categoryCodes = Category::query()
                ->where('parent_id', $categoryList['id'])
                ->pluck('code')
                ->toArray();
            // 将当前父类的 code 也加入数组
            $categoryCodes[] = $request->category_code;
        } else {
            // 如果是子类，直接使用当前类的 code
            $categoryCodes = [$request->category_code];
        }

        // 获取所有相关的文章ID
        $postIds = DB::table('category_post')
            ->whereIn('category_code', $categoryCodes)
            ->pluck('post_id');

        // 构建查询
        $query = Post::whereIn('id', $postIds)->orderBy('sort', 'asc');

        // 添加标题搜索条件
        if ($request->post_title) {
            $query->where('post_title', 'like', '%' . $request->post_title . '%');
        }

        $posts = $query->get();

        // 获取当前用户（如果已登录）
        $currentUser = $request->user();
        $userId = $currentUser ? $currentUser->id : null;

        // 如果用户已登录，获取用户的所有收藏记录
        $userCollects = [];
        if ($userId) {
            $userCollects = Collect::where('user_id', $userId)
                ->whereIn('post_id', $posts->pluck('id'))
                ->pluck('post_id')
                ->toArray();
        }

        // 获取 VARIABLE_URL
        $variableUrl = env('VARIABLE_URL');

        // 转换为数组并处理
        $postsData = $posts->map(function ($post) use ($variableUrl, $userCollects) {
            $postArray = $post->toArray();
            // 处理 thumbnail
            if (isset($postArray['thumbnail'])) {
                $postArray['thumbnail'] = $this->replaceDomain($postArray['thumbnail'], $variableUrl);
            }
            // 处理 more 字段
            if (isset($postArray['more']) && is_array($postArray['more']) && isset($postArray['more']['model'])) {
                $postArray['more']['model'] = $this->replaceDomain($postArray['more']['model'], $variableUrl);
            }
            // 添加收藏状态
            $postArray['is_collected'] = in_array($post->id, $userCollects);
            return $postArray;
        })->all();

        return ['code' => 200, 'data' => $postsData];
    }
    /**
     * 替换 URL 中的域名部分
     */
    private function replaceDomain($url, $newDomain)
    {
        if (!$url || !is_string($url) || !$newDomain) {
            return $url;
        }

        $parsedUrl = parse_url($url);
        if ($parsedUrl === false || !isset($parsedUrl['path'])) {
            return $url;
        }

        $path = $parsedUrl['path'] ?? '';
        return rtrim($newDomain, '/') . '/' . ltrim($path, '/');
    }
}

