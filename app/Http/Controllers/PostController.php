<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Collect;
use App\Models\Post;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class PostController extends Controller
{
    public function index(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'category_code' => 'required|string',
        ]);
        if ($validator->fails()) {
            return ['code' => 422, 'message' => $validator->errors()];
        }

        // 获取当前分类
        $categoryList = Category::query()->where('code', $request->category_code)->first();
        if (!$categoryList) {
            return ['code' => 404, 'message' => 'Category not found'];
        }

        // 如果有标题搜索条件，则按标题搜索逻辑处理
        if ($request->post_title) {
            $categoryCodes = $this->getCategoryCodesByPostTitle($request->post_title);
        } else {
            // 没有标题搜索时，按原逻辑处理
            $topParentCategory = $this->findTopParentCategory($categoryList);

            // 获取顶级父类及其所有子类的 code
            $categoryCodes = Category::query()
                ->where('parent_id', $topParentCategory['id'])
                ->pluck('code')
                ->toArray();
            // 将顶级父类的 code 也加入数组
            $categoryCodes[] = $topParentCategory['code'];
        }

        // 获取所有相关的文章ID
        $postIds = DB::table('category_post')
            ->whereIn('category_code', $categoryCodes)
            ->pluck('post_id');

        // 构建查询
        $query = Post::whereIn('id', $postIds)->orderBy('sort', 'asc');

        // 如果有标题搜索条件，再次过滤标题
        if ($request->post_title) {
            $query->where('post_title', 'like', '%' . $request->post_title . '%');
        }

        $posts = $query->get();

        // 获取当前用户（如果已登录）
        $currentUser = $request->user();
        $userId = $currentUser ? $currentUser->id : null;

        // 如果用户已登录，获取用户的所有收藏记录
        $userCollects = [];
        if ($userId) {
            $userCollects = Collect::where('user_id', $userId)
                ->whereIn('post_id', $posts->pluck('id'))
                ->pluck('post_id')
                ->toArray();
        }

        // 获取 VARIABLE_URL
        $variableUrl = env('VARIABLE_URL');

        // 转换为数组并处理
        $postsData = $posts->map(function ($post) use ($variableUrl, $userCollects) {
            $postArray = $post->toArray();
            // 处理 thumbnail
            if (isset($postArray['thumbnail'])) {
                $postArray['thumbnail'] = $this->replaceDomain($postArray['thumbnail'], $variableUrl);
            }
            // 处理 more 字段
            if (isset($postArray['more']) && is_array($postArray['more']) && isset($postArray['more']['model'])) {
                $postArray['more']['model'] = $this->replaceDomain($postArray['more']['model'], $variableUrl);
            }
            // 添加收藏状态
            $postArray['is_collected'] = in_array($post->id, $userCollects);
            return $postArray;
        })->all();

        return ['code' => 200, 'data' => $postsData];
    }
    /**
     * 根据标题搜索获取相关的分类代码
     */
    private function getCategoryCodesByPostTitle($postTitle)
    {
        // 1. 首先通过 post_title 找到匹配的资源
        $matchingPosts = Post::where('post_title', 'like', '%' . $postTitle . '%')
            ->pluck('id')
            ->toArray();

        if (empty($matchingPosts)) {
            return []; // 如果没有匹配的资源，返回空数组
        }

        // 2. 通过关系表找到这些资源所属的分类
        $relatedCategoryCodes = DB::table('category_post')
            ->whereIn('post_id', $matchingPosts)
            ->pluck('category_code')
            ->unique()
            ->toArray();

        if (empty($relatedCategoryCodes)) {
            return []; // 如果没有关联的分类，返回空数组
        }

        // 3. 找到这些分类的顶级父类
        $topParentCategoryCodes = [];
        foreach ($relatedCategoryCodes as $categoryCode) {
            $category = Category::where('code', $categoryCode)->first();
            if ($category) {
                $topParentCategory = $this->findTopParentCategory($category);
                if ($topParentCategory && !in_array($topParentCategory['code'], $topParentCategoryCodes)) {
                    $topParentCategoryCodes[] = $topParentCategory['code'];
                }
            }
        }

        // 4. 获取所有顶级父类及其子类的 code
        $allCategoryCodes = [];
        foreach ($topParentCategoryCodes as $topParentCode) {
            $topParentCategory = Category::where('code', $topParentCode)->first();
            if ($topParentCategory) {
                // 添加顶级父类本身
                $allCategoryCodes[] = $topParentCode;

                // 添加所有子类
                $childCategoryCodes = Category::where('parent_id', $topParentCategory['id'])
                    ->pluck('code')
                    ->toArray();
                $allCategoryCodes = array_merge($allCategoryCodes, $childCategoryCodes);
            }
        }

        return array_unique($allCategoryCodes);
    }

    /**
     * 递归查找最顶级的父类
     */
    private function findTopParentCategory($category)
    {
        // 如果当前分类没有父类，则它就是最顶级的父类
        if (!$category['parent_id']) {
            return $category;
        }

        // 否则递归查找父类
        $parentCategory = Category::query()->where('id', $category['parent_id'])->first();
        if ($parentCategory) {
            return $this->findTopParentCategory($parentCategory);
        }

        // 如果找不到父类，返回当前分类
        return $category;
    }

    /**
     * 替换 URL 中的域名部分
     */
    private function replaceDomain($url, $newDomain)
    {
        if (!$url || !is_string($url) || !$newDomain) {
            return $url;
        }

        $parsedUrl = parse_url($url);
        if ($parsedUrl === false || !isset($parsedUrl['path'])) {
            return $url;
        }

        $path = $parsedUrl['path'] ?? '';
        return rtrim($newDomain, '/') . '/' . ltrim($path, '/');
    }
}

