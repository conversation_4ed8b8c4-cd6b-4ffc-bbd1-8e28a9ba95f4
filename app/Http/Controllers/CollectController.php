<?php

namespace App\Http\Controllers;

use App\Models\Collect;
use App\Models\Post;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class CollectController extends Controller
{
    /**
     * 创建文件夹
     *
     * @param Request $request
     * @return array
     */
    public function createFolder(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50',
            'parent_id' => 'nullable|integer|exists:collect,id',
        ]);

        if ($validator->fails()) {
            return ['code' => 3001, 'message' => $validator->errors()->first(), 'data' => $validator->errors()];
        }

        $user = $request->user();
        $parentId = $request->parent_id;

        // 验证父文件夹是否存在且属于当前用户
        if ($parentId) {
            $parentFolder = Collect::where('id', $parentId)
                                  ->where('user_id', $user->id)
                                  ->whereNull('post_id')
                                  ->first();
            if (!$parentFolder) {
                return ['code' => 4004, 'message' => '父文件夹不存在'];
            }
        }

        // 支持无限层级创建文件夹，移除层级限制

        // 检查同级目录下是否已存在同名文件夹
        $existingFolder = Collect::where('user_id', $user->id)
                                ->where('parent_id', $parentId)
                                ->where('name', $request->name)
                                ->whereNull('post_id')
                                ->first();
        if ($existingFolder) {
            return ['code' => 3002, 'message' => '同级目录下已存在同名文件夹'];
        }

        try {
            $folder = Collect::create([
                'name' => $request->name,
                'parent_id' => $parentId,
                'user_id' => $user->id,
                'post_id' => null,
            ]);

            return [
                'code' => 200,
                'message' => '文件夹创建成功',
                'data' => [
                    'folder_id' => $folder->id,
                    'name' => $folder->name,
                    'parent_id' => $folder->parent_id,
                    'full_path' => $folder->getFullPath(),
                    'created_at' => $folder->created_at
                ]
            ];
        } catch (\Exception $e) {
            Log::error('文件夹创建失败: ' . $e->getMessage());
            return ['code' => 5001, 'message' => '文件夹创建失败，请稍后重试'];
        }
    }
    /**
     * 添加收藏
     *
     * @param Request $request
     * @return array
     */
    public function createCollect(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'post_id' => 'required|integer|exists:post,id',
            'folder_id' => 'nullable|integer|exists:collect,id',
        ]);

        if ($validator->fails()) {
            return ['code' => 3001, 'message' => $validator->errors()->first(), 'data' => $validator->errors()];
        }
        $user = $request->user();
        $postId = $request->post_id;
        $folderId = $request->folder_id;

        // 验证文件夹是否存在且属于当前用户
        if ($folderId) {
            $folder = Collect::where('id', $folderId)
                            ->where('user_id', $user->id)
                            ->whereNull('post_id')
                            ->first();
            if (!$folder) {
                return ['code' => 4004, 'message' => '文件夹不存在'];
            }
        }

        $existingCollect = Collect::where('user_id', $user->id)
                                 ->where('post_id', $postId)
                                 ->first();
        if ($existingCollect) {
            return ['code' => 3002, 'message' => '您已经收藏过这个资源了'];
        }
        try {
            $collect = Collect::create([
                'parent_id' => $folderId,
                'user_id' => $user->id,
                'post_id' => $postId,
            ]);
            return [
                'code' => 200,
                'message' => '收藏成功',
                'data' => [
                    'collect_id' => $collect->id,
                    'post_id' => $postId,
                    'folder_id' => $folderId,
                    'created_at' => $collect->created_at
                ]
            ];
        } catch (\Exception $e) {
            Log::error('收藏失败: ' . $e->getMessage());
            return ['code' => 5001, 'message' => '收藏失败，请稍后重试'];
        }
    }

    /**
     * 取消收藏
     *
     * @param Request $request
     * @return array
     */
    public function cancelCollect(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'post_id' => 'required|integer|exists:post,id',
        ]);
        if ($validator->fails()) {
            return ['code' => 3001, 'message' => $validator->errors()->first(), 'data' => $validator->errors()];
        }
        $user = $request->user();
        $postId = $request->post_id;
        $collect = Collect::where('user_id', $user->id)
                         ->where('post_id', $postId)
                         ->first();
        if (!$collect) {
            return ['code' => 4004, 'message' => '您还没有收藏过这个资源'];
        }
        try {
            $collect->delete();
            return [
                'code' => 200,
                'message' => '取消收藏成功',
                'data' => [
                    'post_id' => $postId
                ]
            ];
        } catch (\Exception $e) {
            Log::error('取消收藏失败: ' . $e->getMessage());
            return ['code' => 5001, 'message' => '取消收藏失败，请稍后重试'];
        }
    }
    /**
     * 获取收藏列表（树状结构）
     * 返回完整的树状结构，包含文件夹和收藏资源
     *
     * @param Request $request
     * @return array
     */
    public function collectList(Request $request)
    {
        $user = $request->user();

        try {
            // 获取用户的所有收藏数据（包括文件夹和收藏资源）
            $allCollects = Collect::with(['post' => function($query) {
                $query->select('*');
            }])
            ->where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->get();

            // 构建树状结构
            $treeData = $this->buildCollectTree($allCollects);

            return [
                'code' => 200,
                'message' => '获取收藏列表成功',
                'data' => [
                    'data' => $treeData,
                    'total' => $allCollects->count(),
                    'folder_count' => $allCollects->where('post_id', null)->count(),
                    'resource_count' => $allCollects->where('post_id', '!=', null)->count()
                ]
            ];
        } catch (\Exception $e) {
            Log::error('获取收藏列表失败: ' . $e->getMessage());
            return ['code' => 5001, 'message' => '获取收藏列表失败，请稍后重试'];
        }
    }

    /**
     * 构建收藏树状结构
     *
     * @param $collects
     * @param $parentId
     * @return array
     */
    private function buildCollectTree($collects, $parentId = null)
    {
        $tree = [];

        foreach ($collects as $collect) {
            if ($collect->parent_id == $parentId) {
                $node = [
                    'id' => $collect->id,
                    'name' => $collect->name,
                    'parent_id' => $collect->parent_id,
                    'user_id' => $collect->user_id,
                    'post_id' => $collect->post_id,
                    'created_at' => $collect->created_at,
                    'updated_at' => $collect->updated_at,
                    'type' => $collect->post_id ? 'resource' : 'folder', // 标识类型
                    'post' => $collect->post, // 关联的资源信息
                    'children' => []
                ];

                // 递归获取子节点
                $children = $this->buildCollectTree($collects, $collect->id);
                if (!empty($children)) {
                    $node['children'] = $children;
                }

                $tree[] = $node;
            }
        }

        return $tree;
    }

    /**
     * 检查资源是否已被收藏
     *
     * @param Request $request
     * @return array
     */
    public function checkCollect(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'post_id' => 'required|integer|exists:post,id',
        ]);

        if ($validator->fails()) {
            return ['code' => 3001, 'message' => $validator->errors()->first(), 'data' => $validator->errors()];
        }
        $user = $request->user();
        $postId = $request->post_id;
        $isCollected = Collect::isCollected($user->id, $postId);
        return [
            'code' => 200,
            'message' => '检查成功',
            'data' => [
                'post_id' => $postId,
                'is_collected' => $isCollected
            ]
        ];
    }
    /**
     * 获取用户收藏统计信息
     *
     * @param Request $request
     * @return array
     */
    public function collectStats(Request $request)
    {
        $user = $request->user();

        try {
            $totalCount = Collect::getCollectCount($user->id);

            return [
                'code' => 200,
                'message' => '获取统计信息成功',
                'data' => [
                    'total_collect_count' => $totalCount
                ]
            ];
        } catch (\Exception $e) {
            Log::error('获取收藏统计失败: ' . $e->getMessage());
            return ['code' => 5001, 'message' => '获取统计信息失败，请稍后重试'];
        }
    }
}
