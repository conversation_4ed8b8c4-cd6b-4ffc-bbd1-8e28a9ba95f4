<?php

namespace App\Http\Controllers;

use App\Models\NounStructure;
use App\Services\Common\OfficeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class NounStructureController extends Controller
{
    /**
     * 获取结构名词列表
     *
     * @param Request $request
     * @return array
     */
    public function index(Request $request)
    {
        try {
            $query = NounStructure::query();

            // 搜索功能
            if ($request->has('search') && !empty($request->search)) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('key', 'like', "%{$search}%")
                      ->orWhere('noun_ch', 'like', "%{$search}%")
                      ->orWhere('noun_en', 'like', "%{$search}%");
                });
            }

            // 排序
            $query->orderBy('created_at', 'desc');

            // 获取所有数据，不分页
            $nounStructures = $query->get();

            return [
                'code' => 200,
                'message' => '获取结构名词列表成功',
                'data' => [
                    'data' => $nounStructures,
                    'total' => $nounStructures->count()
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取结构名词列表失败: ' . $e->getMessage());
            return ['code' => 5001, 'message' => '获取列表失败'];
        }
    }

    /**
     * 获取统计信息
     *
     * @return array
     */
    public function stats()
    {
        try {
            $total = NounStructure::count();
            $completeCount = NounStructure::whereNotNull('noun_ch')
                                         ->whereNotNull('note_ch')
                                         ->whereNotNull('noun_en')
                                         ->whereNotNull('note_en')
                                         ->where('noun_ch', '!=', '')
                                         ->where('note_ch', '!=', '')
                                         ->where('noun_en', '!=', '')
                                         ->where('note_en', '!=', '')
                                         ->count();

            $incompleteCount = $total - $completeCount;

            return [
                'code' => 200,
                'message' => '获取统计信息成功',
                'data' => [
                    'total_count' => $total,
                    'complete_count' => $completeCount,
                    'incomplete_count' => $incompleteCount,
                    'completion_rate' => $total > 0 ? round(($completeCount / $total) * 100, 2) : 0
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取统计信息失败: ' . $e->getMessage());
            return ['code' => 5001, 'message' => '获取统计信息失败'];
        }
    }
}
