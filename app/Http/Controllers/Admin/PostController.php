<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\CategoryPost;
use App\Models\Post;
use App\Models\User;
use App\Services\Common\OfficeService;
use App\Services\PostService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class PostController extends Controller
{
    public function importExcel(Request $request, Post $post)
    {
        $rules = [
            'post_type' => ['required', 'integer', Rule::in(Post::getPostTypes())],
            'upload' => ['required', 'file'],
            'category_code' => ['required', 'string'],
            'is_import' => ['required', 'integer', 'in:0,1']
        ];
        $customAttributes = [
            'upload' => 'Excel文件',
            'post_type' => '文章类型'
        ];
        $validator = Validator::make($request->all(), $rules, [], $customAttributes);
        // 验证失败则返回错误
        if ($validator->fails()) {
            return ['code' => 3001, 'message' => $validator->errors()->first(), 'data' => $validator->errors()];
        }
        $file = $request->file('upload');
        // 文件是否上传成功
        if (!$file || !$file->isValid()) {
            return ['code' => 3001, 'message' => '文件上传失败'];
        }
        // 获取文件相关信息
        $ext = $file->getClientOriginalExtension();     // 扩展名
        $realPath = $file->getRealPath();   //临时文件的绝对路径
        // 上传文件路径和名称
        $filename = 'excel/importPost.' . $ext;
        $bool = \Illuminate\Support\Facades\Storage::disk('public')->put($filename, file_get_contents($realPath));
        if ($bool) {
            @unlink($realPath);
            $filePath = config('filesystems.disks.public.root') . '/' . $filename;
        } else {
            return ['code' => 3001, 'message' => '文件上传失败'];
        }
        try {
//            $rows = Excel::toArray(new PostsImpor(), storage_path('app/public/' . $filename));
            $rows = OfficeService::importExecl($filePath, 15, 1000);
        } catch (\Exception $exc) {
            @unlink($filePath);
            return ['code' => 3001, 'message' => $exc->getMessage()];
        }

        $str = '';
        foreach ($rows as $rowKey => $row) {
            if ($rowKey == 1) {
                //首行忽略
                continue;
            }
            $sn = trim($row['A']);
            $fn = intval($row['E']);
            if (!$sn || !$fn || $fn != 2) {
                continue;
            }
            $str .= "'$sn',";
        }
        $errors = []; //抛出验证错误
        $succ = []; //成功数据
        // 获取当前最大 sort 值
        $maxSort = Post::max('sort');
        $sortCounter = $maxSort + 1;
        foreach ($rows as $rowKey => $row) {
            if ($rowKey == 1) {
                //首行忽略
                continue;
            }
            $photos = [];
            if ($row["I"] && $request->post_type == 3) {
                //图集
                $row["I"] = str_replace('，', ',', $row["I"]);
                $photos = explode(',', $row["I"]);
            }
//            $category_ids_import = PostService::getLastCategoryForImport($row["K"]);
            $more_ext = [
                'photos' => $photos,
                'section_width' => $row['N'],
                'section_height' => $row['O'],
            ];
            $handleData = [
                'post_type' => (int)$request->post_type,
                'post_sn' => trim($row['A']),
                'cost_attribute' => 2,
                'coin' => 0,
                'post_status' => (int)$row["E"],
                'comment_status' => 2,
                'is_top' => 2,
                'recommended' => 2,
                'post_title' => trim($row["B"]),
                'post_keywords' => trim($row["C"]),
                'post_excerpt' => trim($row["D"]),
                'thumbnail' => PostService::getThumbnailByPostSn($request->post_type, $row['A']),
                'more' => PostService::getPostMoreDataByImport($request->post_type, $row['A'], $more_ext),
                'sort' => $sortCounter,
            ];
            $sortCounter++;
            if ($handleData) {
                //处理字段值 && 过滤空值
                $handleData = collect($handleData)
                    ->reject(function ($value, $key) {
                        if (empty($value)) {
                            return true;
                        }
                    })
                    ->toArray();
            }
//            $rules = array_merge_recursive($post->rules($request), [
////                'post_sn' => [Rule::unique('post')],
//            ]);
//            $customAttributes = array_merge_recursive($post->customAttributes(), []);
//            $validator = Validator::make($handleData, $rules, $customAttributes);
//            if ($validator->fails()) {
//                $errors[] = [
//                    'line' => $rowKey,
//                    'message' => implode('', $validator->errors()->all()),
//                ];
//                continue;
//            }
//            if ($request->is_import == 0) {
//                $succ[] = ['line' => $rowKey];
//            }
            if ($request->is_import == 1) {

                $post = new Post();
                $post->fill($handleData);
//                $post->is_import = 1;
                $keywords = explode(',', str_replace('，', ',', $post->post_keywords));
                DB::beginTransaction();
                if ($post->save()) {
                    if (!CategoryPost::where('category_code', $request->category_code)->where('post_id', $post->id)->exists()) {
                        // 插入新的关联关系
                        $categoryPost = new CategoryPost();
                        $categoryPost->category_code = $request->category_code;
                        $categoryPost->post_id = $post->id;
                        DB::table('category_post')->insert([
                            'post_id' => $post->id,
                            'category_code' => $request->category_code,
                            'created_at' => date('Y-m-d H:i:s'),
                        ]);
//                        $categoryPost->save();
                    }
                    DB::commit();
                    $succ[] = ['line' => $rowKey];
                } else {
                    DB::rollBack();
                }
            }
        }
        $total_num = count($rows) - 1;
        $fail_num = count($errors);
        $succ_num = count($succ);
        return [
            'code' => 200,
            'message' => 'success',
            'data' => [
                'total_num' => $total_num,
                'fail_num' => $fail_num,
                'succ_num' => $succ_num,
                'errors' => $errors,
                'succ' => $succ,
            ]
        ];
    }
    public function importExcel1(Request $request, Post $post)
    {
        $rules = [
            'post_type' => ['required', 'integer', Rule::in(Post::getPostTypes())],
            'upload' => ['required', 'file'],
            'category_code' => ['required', 'string'],
            'is_import' => ['required', 'integer', 'in:0,1']
        ];
        $customAttributes = [
            'upload' => 'Excel文件',
            'post_type' => '文章类型'
        ];
        $validator = Validator::make($request->all(), $rules, [], $customAttributes);
        // 验证失败则返回错误
        if ($validator->fails()) {
            return ['code' => 3001, 'message' => $validator->errors()->first(), 'data' => $validator->errors()];
        }
        $file = $request->file('upload');
        // 文件是否上传成功
        if (!$file || !$file->isValid()) {
            return ['code' => 3001, 'message' => '文件上传失败'];
        }
        // 获取文件相关信息
        $ext = $file->getClientOriginalExtension();     // 扩展名
        $realPath = $file->getRealPath();   //临时文件的绝对路径
        // 上传文件路径和名称
        $filename = 'excel/importPost.' . $ext;
        $bool = \Illuminate\Support\Facades\Storage::disk('public')->put($filename, file_get_contents($realPath));
        if ($bool) {
            @unlink($realPath);
            $filePath = config('filesystems.disks.public.root') . '/' . $filename;
        } else {
            return ['code' => 3001, 'message' => '文件上传失败'];
        }
        try {
//            $rows = Excel::toArray(new PostsImpor(), storage_path('app/public/' . $filename));
            $rows = OfficeService::importExecl($filePath, 15, 1000);
        } catch (\Exception $exc) {
            @unlink($filePath);
            return ['code' => 3001, 'message' => $exc->getMessage()];
        }

        $str = '';
        foreach ($rows as $rowKey => $row) {
            if ($rowKey == 1) {
                //首行忽略
                continue;
            }
            $sn = trim($row['A']);
            $fn = intval($row['E']);
            if (!$sn || !$fn || $fn != 2) {
                continue;
            }
            $str .= "'$sn',";
        }
        $errors = []; //抛出验证错误
        $succ = []; //成功数据
        foreach ($rows as $rowKey => $row) {
            if ($rowKey == 1) {
                //首行忽略
                continue;
            }
            $photos = [];
            if ($row["I"] && $request->post_type == 3) {
                //图集
                $row["I"] = str_replace('，', ',', $row["I"]);
                $photos = explode(',', $row["I"]);
            }
//            $category_ids_import = PostService::getLastCategoryForImport($row["K"]);
            $more_ext = [
                'photos' => $photos,
                'section_width' => $row['N'],
                'section_height' => $row['O'],
            ];
            $handleData = [
                'post_type' => (int)$request->post_type,
                'post_sn' => trim($row['A']),
                'cost_attribute' => 2,
                'coin' => 0,
                'post_status' => (int)$row["E"],
                'comment_status' => 2,
                'is_top' => 2,
                'recommended' => 2,
                'post_title' => trim($row["B"]),
                'post_keywords' => trim($row["C"]),
                'post_excerpt' => trim($row["D"]),
                'thumbnail' => PostService::getThumbnailByPostSn($request->post_type, $row['A']),
                'more' => PostService::getPostMoreDataByImport($request->post_type, $row['A'], $more_ext),
                'sort' => 1000,
            ];
            if ($handleData) {
                //处理字段值 && 过滤空值
                $handleData = collect($handleData)
                    ->reject(function ($value, $key) {
                        if (empty($value)) {
                            return true;
                        }
                    })
                    ->toArray();
            }
            $rules = array_merge_recursive($post->rules($request), [
//                'post_sn' => [Rule::unique('post')],
            ]);
            $customAttributes = array_merge_recursive($post->customAttributes(), []);
            $validator = Validator::make($handleData, $rules, $customAttributes);
            if ($validator->fails()) {
                $errors[] = [
                    'line' => $rowKey,
                    'message' => implode('', $validator->errors()->all()),
                ];
                continue;
            }
            if ($request->is_import == 0) {
                $succ[] = ['line' => $rowKey];
            }
            if ($request->is_import == 1) {

                $post = new Post();
                $post->fill($handleData);
//                $post->is_import = 1;
                $keywords = explode(',', str_replace('，', ',', $post->post_keywords));
                DB::beginTransaction();
                if ($post->save()) {
                    if (!CategoryPost::where('category_code', $request->category_code)->where('post_id', $post->id)->exists()) {
                        // 插入新的关联关系
                        $categoryPost = new CategoryPost();
                        $categoryPost->category_code = $request->category_code;
                        $categoryPost->post_id = $post->id;
                        DB::table('category_post')->insert([
                            'post_id' => $post->id,
                            'category_code' => $request->category_code,
                            'created_at' => date('Y-m-d H:i:s'),
                        ]);
//                        $categoryPost->save();
                    }
                    DB::commit();
                    $succ[] = ['line' => $rowKey];
                } else {
                    DB::rollBack();
                }
            }
        }
        $total_num = count($rows) - 1;
        $fail_num = count($errors);
        $succ_num = count($succ);
        return [
            'code' => 200,
            'message' => 'success',
            'data' => [
                'total_num' => $total_num,
                'fail_num' => $fail_num,
                'succ_num' => $succ_num,
                'errors' => $errors,
                'succ' => $succ,
            ]
        ];
    }

    public function index(Request $request)
    {
        // 获取资源列表并关联 categories 目录信息
        $model = Post::with('categories')
        ->orderBy('created_at', 'desc');

        if (isset($request->page) && $request->page <= 0) {
            $rows = $model->limit(10)->get();
        } else {
            $rows = $model->paginate();
        }

        // 返回资源列表及分页信息
        return ['code'=>200,'data'=>$rows];
    }

    public function updateSort(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|integer',
            'sort' => 'required|integer',
        ]);
        if ($validator->fails()) {
            return ['code' => 3001, 'message' => $validator->errors()->first(), 'data' => $validator->errors()];
        }
        $model = Post::find($request->id);
        $model->fill($request->all());
        if ($model->save()) {
            return ['code' => 200, 'message' =>'更新成功', 'data' => $model];
        } else {
            return ['code' => 5001, 'message' =>'更新失败'];
        }

    }

}
