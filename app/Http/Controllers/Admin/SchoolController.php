<?php

namespace App\Http\Controllers\Admin;

use App\Models\School;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\Controller;
use App\Models\User;

class SchoolController extends Controller
{
    /**
     * 获取学校列表
     *
     * @param Request $request
     * @return array
     */
    public function index(Request $request)
    {
        $query = School::query()->orderByDesc('id');

        // 按名称搜索
        if ($request->has('name') && !empty($request->name)) {
            $query->where('name', 'like', "%{$request->name}%");
        }

        // 分页或获取全部
        if (isset($request->page) && $request->page <= 0) {
            $schools = $query->get();
        } else {
            $schools = $query->paginate($request->per_page ?? 15);
        }

        return ['code' => 200, 'message' => 'Success', 'data' => $schools];
    }

    /**
     * 添加学校
     *
     * @param Request $request
     * @return array
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:school,name'
        ]);

        if ($validator->fails()) {
            return ['code' => 3001, 'message' => $validator->errors()->first(), 'data' => $validator->errors()];
        }

        try {
            $school = School::create([
                'name' => $request->name
            ]);

            return ['code' => 200, 'message' => 'School created successfully', 'data' => $school];
        } catch (\Exception $e) {
            return ['code' => 5001, 'message' => 'Failed to create school: ' . $e->getMessage()];
        }
    }

    /**
     * 获取单个学校信息
     *
     * @param int $id
     * @return array
     */
    public function show(Request $request)
    {
        $id = $request->input('id');
        if(!$id){
            return ['code' => 3001, 'message' => 'id is required'];
        }
        try {
            $school = School::findOrFail($id);
            return ['code' => 200, 'message' => 'Success', 'data' => $school];
        } catch (\Exception $e) {
            return ['code' => 4004, 'message' => 'School not found'];
        }
    }

    /**
     * 更新学校信息
     *
     * @param Request $request
     * @param int $id
     * @return array
     */
    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id'=>"required|int",
            'name' => "required|string|max:255"
        ]);

        if ($validator->fails()) {
            return ['code' => 3001, 'message' => $validator->errors()->first(), 'data' => $validator->errors()];
        }
        try {
            $school = School::findOrFail($request->id);
            $school->name = $request->name;
            $school->save();

            return ['code' => 200, 'message' => 'School updated successfully', 'data' => $school];
        } catch (\Exception $e) {
            return ['code' => 5001, 'message' => 'Failed to update school: ' . $e->getMessage()];
        }
    }

    /**
     * 删除学校（支持单个或批量删除）
     *
     * @param Request $request
     * @return array
     */
    public function destroy(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => ['required', 'array'],
        ]);
        if ($validator->fails()) {
            return ['code' => 3001, 'message' => $validator->errors()->first(), 'data' => $validator->errors()];
        }
        $model = School::whereIn('id', is_array($request->id) ? $request->id : [$request->id]);
        if ($model->delete()) {
            return ['code' => 200, 'message' => __('common.Success')];
        } else {
            return ['code' => 5001, 'message' => __('common.Server internal error')];
        }
    }

    /**
     * 批量更新学校学生的有效期
     *
     * @param Request $request
     * @return array
     */
    public function updateExpireTime(Request $request)
    {
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'school_id' => 'required|integer|exists:school,id',
            'validity_days' => 'required|integer|min:1',
        ]);

        if ($validator->fails()) {
            return ['code' => 3001, 'message' => $validator->errors()->first(), 'data' => $validator->errors()];
        }

        try {
            $schoolId = $request->school_id;
            $validityDays = $request->validity_days;

            // 计算新的过期时间
            $newExpireTime = now()->addDays($validityDays);

            // 查找该学校的所有未删除用户
            $users = User::where('school_id', $schoolId)
                         ->whereNull('deleted_at')
                         ->get();

            if ($users->isEmpty()) {
                return ['code' => 4004, 'message' => 'No users found for this school'];
            }

            // 批量更新用户的过期时间
            $updateCount = User::where('school_id', $schoolId)
                              ->whereNull('deleted_at')
                              ->update(['expire_time' => $newExpireTime]);

            // 获取学校名称
            $school = School::find($schoolId);

            return [
                'code' => 200,
                'message' => 'Successfully updated expiration time for users',
                'data' => [
                    'school_name' => $school->name,
                    'updated_users_count' => $updateCount,
                    'new_expire_time' => $newExpireTime->format('Y-m-d H:i:s'),
                    'validity_days' => $validityDays
                ]
            ];
        } catch (\Exception $e) {
            return ['code' => 5001, 'message' => 'Failed to update expiration time: ' . $e->getMessage()];
        }
    }
}
