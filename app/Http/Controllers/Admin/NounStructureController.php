<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\NounStructure;
use App\Services\Common\OfficeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class NounStructureController extends Controller
{
    /**
     * 导入结构名词数据
     * 先导入结构名词1.xlsx，再用结构名词3.xlsx补充空字段
     *
     * @return array
     */
    public function importNounStructure()
    {
        try {
            // 第一步：导入结构名词1.xlsx（主数据源）
            $result1 = $this->importFirstExcel();
            if ($result1['code'] !== 200) {
                return $result1;
            }

            // 第二步：用结构名词3.xlsx补充数据（填补空字段和添加新数据）
            $result2 = $this->supplementWithThirdExcel();
            if ($result2['code'] !== 200) {
                return $result2;
            }

            // 第三步：添加结构名词3.xlsx中存在但结构名词1.xlsx中不存在的数据
            $result3 = $this->addMissingFromThirdExcel();
            if ($result3['code'] !== 200) {
                return $result3;
            }

            return [
                'code' => 200,
                'message' => '结构名词数据导入完成',
                'data' => [
                    'first_excel_result' => $result1['data'],
                    'supplement_result' => $result2['data'],
                    'missing_data_result' => $result3['data']
                ]
            ];

        } catch (\Exception $e) {
            Log::error('结构名词导入失败: ' . $e->getMessage());
            return ['code' => 5001, 'message' => '导入失败: ' . $e->getMessage()];
        }
    }

    /**
     * 导入结构名词1.xlsx
     *
     * @return array
     */
    private function importFirstExcel()
    {
        $filePath = storage_path('app/public/excel/结构名词1.xlsx');

        if (!file_exists($filePath)) {
            return ['code' => 4004, 'message' => '结构名词1.xlsx 文件不存在'];
        }

        try {
            // 使用 OfficeService 读取 Excel 文件
            $sheet = OfficeService::importExecl($filePath);

            if (empty($sheet) || count($sheet) < 2) {
                return ['code' => 3001, 'message' => '结构名词1.xlsx 文件格式不正确，至少需要包含表头和一行数据'];
            }

            $successCount = 0;
            $failedCount = 0;
            $failedRows = [];

            // 遍历所有行，跳过表头行
            foreach ($sheet as $rowIndex => $row) {
                // 跳过表头行（第一行）
                if ($rowIndex <= 1) {
                    continue;
                }

                // 检查必要的列是否存在
                if (!isset($row['B']) || !isset($row['C']) || !isset($row['D']) || !isset($row['E']) || !isset($row['F'])) {
                    continue;
                }

                $key = trim($row['B']);
                $nounCh = trim($row['C']);
                $noteCh = trim($row['D']);
                $nounEn = trim($row['E']);
                $noteEn = trim($row['F']);

                // 如果key为空，跳过这一行
                if (empty($key)) {
                    continue;
                }

                try {
                    // 检查是否已存在相同的key
                    $existing = NounStructure::where('key', $key)->first();

                    if ($existing) {
                        // 如果已存在，更新数据
                        $existing->update([
                            'noun_ch' => $nounCh ?: $existing->noun_ch,
                            'note_ch' => $noteCh ?: $existing->note_ch,
                            'noun_en' => $nounEn ?: $existing->noun_en,
                            'note_en' => $noteEn ?: $existing->note_en,
                        ]);
                    } else {
                        // 如果不存在，创建新记录
                        NounStructure::create([
                            'key' => $key,
                            'noun_ch' => $nounCh,
                            'note_ch' => $noteCh,
                            'noun_en' => $nounEn,
                            'note_en' => $noteEn,
                        ]);
                    }

                    $successCount++;
                } catch (\Exception $e) {
                    $failedCount++;
                    $failedRows[] = [
                        'row' => $rowIndex,
                        'key' => $key,
                        'error' => $e->getMessage()
                    ];
                    Log::error("结构名词1.xlsx 第{$rowIndex}行导入失败，key: {$key}，错误信息：" . $e->getMessage());
                }
            }

            return [
                'code' => 200,
                'message' => "结构名词1.xlsx 导入完成，成功 {$successCount} 条，失败 {$failedCount} 条",
                'data' => [
                    'success_count' => $successCount,
                    'failed_count' => $failedCount,
                    'failed_rows' => $failedRows
                ]
            ];

        } catch (\Exception $e) {
            Log::error('结构名词1.xlsx 读取失败: ' . $e->getMessage());
            return ['code' => 3001, 'message' => '结构名词1.xlsx 读取失败：' . $e->getMessage()];
        }
    }

    /**
     * 用结构名词3.xlsx补充数据
     *
     * @return array
     */
    private function supplementWithThirdExcel()
    {
        $filePath = storage_path('app/public/excel/结构名词3.xlsx');

        if (!file_exists($filePath)) {
            return ['code' => 4004, 'message' => '结构名词3.xlsx 文件不存在'];
        }

        try {
            // 使用 OfficeService 读取 Excel 文件
            $sheet = OfficeService::importExecl($filePath);

            if (empty($sheet) || count($sheet) < 2) {
                return ['code' => 3001, 'message' => '结构名词3.xlsx 文件格式不正确，至少需要包含表头和一行数据'];
            }

            $matchedCount = 0;
            $updatedCount = 0;
            $notFoundCount = 0;
            $noUpdateNeededCount = 0;
            $updateDetails = [];

            // 遍历所有行，跳过表头行
            foreach ($sheet as $rowIndex => $row) {
                // 跳过表头行（第一行）
                if ($rowIndex <= 1) {
                    continue;
                }

                // 检查必要的列是否存在
                if (!isset($row['B'])) {
                    continue;
                }

                $key = trim($row['B']);

                // 如果key为空，跳过这一行
                if (empty($key)) {
                    continue;
                }

                // 在数据库中查找匹配的记录
                $existing = NounStructure::where('key', $key)->first();

                if (!$existing) {
                    $notFoundCount++;
                    continue;
                }

                $matchedCount++;

                // 检查哪些字段为空，需要补充
                $updateData = [];
                $updatedFields = [];

                if (empty($existing->noun_ch) && isset($row['C']) && !empty(trim($row['C']))) {
                    $updateData['noun_ch'] = trim($row['C']);
                    $updatedFields[] = 'noun_ch';
                }

                if (empty($existing->note_ch) && isset($row['D']) && !empty(trim($row['D']))) {
                    $updateData['note_ch'] = trim($row['D']);
                    $updatedFields[] = 'note_ch';
                }

                if (empty($existing->noun_en) && isset($row['E']) && !empty(trim($row['E']))) {
                    $updateData['noun_en'] = trim($row['E']);
                    $updatedFields[] = 'noun_en';
                }

                if (empty($existing->note_en) && isset($row['F']) && !empty(trim($row['F']))) {
                    $updateData['note_en'] = trim($row['F']);
                    $updatedFields[] = 'note_en';
                }

                if (!empty($updateData)) {
                    try {
                        $existing->update($updateData);
                        $updatedCount++;
                        $updateDetails[] = [
                            'key' => $key,
                            'updated_fields' => $updatedFields,
                            'update_data' => $updateData
                        ];
                    } catch (\Exception $e) {
                        Log::error("更新key为 {$key} 的记录失败：" . $e->getMessage());
                    }
                } else {
                    $noUpdateNeededCount++;
                }
            }

            return [
                'code' => 200,
                'message' => "结构名词3.xlsx 补充完成，匹配 {$matchedCount} 条，更新 {$updatedCount} 条，无需更新 {$noUpdateNeededCount} 条，未找到 {$notFoundCount} 条",
                'data' => [
                    'matched_count' => $matchedCount,
                    'updated_count' => $updatedCount,
                    'no_update_needed_count' => $noUpdateNeededCount,
                    'not_found_count' => $notFoundCount,
                    'update_details' => $updateDetails
                ]
            ];

        } catch (\Exception $e) {
            Log::error('结构名词3.xlsx 读取失败: ' . $e->getMessage());
            return ['code' => 3001, 'message' => '结构名词3.xlsx 读取失败：' . $e->getMessage()];
        }
    }

    /**
     * 添加结构名词3.xlsx中存在但结构名词1.xlsx中不存在的数据
     *
     * @return array
     */
    private function addMissingFromThirdExcel()
    {
        $filePath = storage_path('app/public/excel/结构名词3.xlsx');

        if (!file_exists($filePath)) {
            return ['code' => 4004, 'message' => '结构名词3.xlsx 文件不存在'];
        }

        try {
            // 使用 OfficeService 读取 Excel 文件
            $sheet = OfficeService::importExecl($filePath);

            if (empty($sheet) || count($sheet) < 2) {
                return ['code' => 3001, 'message' => '结构名词3.xlsx 文件格式不正确，至少需要包含表头和一行数据'];
            }

            $addedCount = 0;
            $skippedCount = 0;
            $addedDetails = [];

            // 遇历所有行，跳过表头行
            foreach ($sheet as $rowIndex => $row) {
                // 跳过表头行（第一行）
                if ($rowIndex <= 1) {
                    continue;
                }

                // 检查必要的列是否存在
                if (!isset($row['B'])) {
                    continue;
                }

                $key = trim($row['B']);

                // 如果key为空，跳过这一行
                if (empty($key)) {
                    continue;
                }

                // 检查数据库中是否已存在该key
                $existing = NounStructure::where('key', $key)->first();

                if ($existing) {
                    // 如果已存在，跳过
                    $skippedCount++;
                    continue;
                }

                // 如果不存在，创建新记录
                try {
                    $nounCh = isset($row['C']) ? trim($row['C']) : '';
                    $noteCh = isset($row['D']) ? trim($row['D']) : '';
                    $nounEn = isset($row['E']) ? trim($row['E']) : '';
                    $noteEn = isset($row['F']) ? trim($row['F']) : '';

                    $newRecord = NounStructure::create([
                        'key' => $key,
                        'noun_ch' => $nounCh,
                        'note_ch' => $noteCh,
                        'noun_en' => $nounEn,
                        'note_en' => $noteEn,
                    ]);

                    $addedCount++;
                    $addedDetails[] = [
                        'key' => $key,
                        'noun_ch' => $nounCh,
                        'noun_en' => $nounEn
                    ];

                } catch (\Exception $e) {
                    Log::error("添加key为 {$key} 的记录失败：" . $e->getMessage());
                }
            }

            return [
                'code' => 200,
                'message' => "从结构名词3.xlsx添加缺失数据完成，新增 {$addedCount} 条，跳过 {$skippedCount} 条",
                'data' => [
                    'added_count' => $addedCount,
                    'skipped_count' => $skippedCount,
                    'added_details' => $addedDetails
                ]
            ];

        } catch (\Exception $e) {
            Log::error('结构名词3.xlsx 读取失败: ' . $e->getMessage());
            return ['code' => 3001, 'message' => '结构名词3.xlsx 读取失败：' . $e->getMessage()];
        }
    }

    /**
     * 获取结构名词列表
     *
     * @param Request $request
     * @return array
     */
    public function index(Request $request)
    {
        try {
            $query = NounStructure::query();

            // 搜索功能
            if ($request->has('search') && !empty($request->search)) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('key', 'like', "%{$search}%")
                      ->orWhere('noun_ch', 'like', "%{$search}%")
                      ->orWhere('noun_en', 'like', "%{$search}%");
                });
            }

            // 排序
            $query->orderBy('created_at', 'desc');

            // 分页
            $perPage = $request->per_page ?? 15;
            $nounStructures = $query->paginate($perPage);

            return [
                'code' => 200,
                'message' => '获取结构名词列表成功',
                'data' => $nounStructures
            ];

        } catch (\Exception $e) {
            Log::error('获取结构名词列表失败: ' . $e->getMessage());
            return ['code' => 5001, 'message' => '获取列表失败'];
        }
    }

    /**
     * 获取统计信息
     *
     * @return array
     */
    public function stats()
    {
        try {
            $total = NounStructure::count();
            $completeCount = NounStructure::whereNotNull('noun_ch')
                                         ->whereNotNull('note_ch')
                                         ->whereNotNull('noun_en')
                                         ->whereNotNull('note_en')
                                         ->where('noun_ch', '!=', '')
                                         ->where('note_ch', '!=', '')
                                         ->where('noun_en', '!=', '')
                                         ->where('note_en', '!=', '')
                                         ->count();

            $incompleteCount = $total - $completeCount;

            return [
                'code' => 200,
                'message' => '获取统计信息成功',
                'data' => [
                    'total_count' => $total,
                    'complete_count' => $completeCount,
                    'incomplete_count' => $incompleteCount,
                    'completion_rate' => $total > 0 ? round(($completeCount / $total) * 100, 2) : 0
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取统计信息失败: ' . $e->getMessage());
            return ['code' => 5001, 'message' => '获取统计信息失败'];
        }
    }
}
