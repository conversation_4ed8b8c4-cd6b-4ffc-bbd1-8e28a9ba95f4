<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class CategoryController extends Controller
{
    // 目录列表
    public function index()
    {
        $categories = Category::with('children')->whereNull('parent_id')->get();
        return ['code'=>200,'data'=>$categories];
    }

    // 新增目录
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(),[
            'parent_id' => 'nullable|exists:category,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'status' => 'required|in:1,0',
            'sort' => 'nullable|integer',
            'code' => 'required|string'
        ]);
        if ($validator->fails()) {
            return ['code' => 3001, 'message' => $validator->errors()->first(), 'data' => $validator->errors()];
        }
        $data = $validator->validated();
        $category = Category::create($data);
        if ($category) {
            return ['code' => 200, 'message' =>'创建成功', 'data' => $data];
        } else {
            return ['code' => 5001, 'message' =>'创建失败'];
        }
    }

    // 显示单个目录
    public function show(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => ['required', 'integer', Rule::exists('category')],
        ]);
        if ($validator->fails()) {
            return ['code' => 3001, 'message' => $validator->errors()->first(), 'data' => $validator->errors()];
        }
        $category = Category::where('id',$request->id)->first();
        if ($category) {
            return ['code' => 200, 'message' => __('common.Success'), 'data' => $category];
        } else {
            return ['code' => 5001, 'message' =>'获取列表失败'];
        }
    }

    // 更新目录
    public function update(Request $request)
    {
        $validator = Validator::make($request->all(),[
            'id' => 'required|integer',
            'parent_id' => 'nullable|exists:category,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'status' => 'required|in:1,0',
            'sort' => 'nullable|integer',
            'code' => 'required|string'
        ]);
        if ($validator->fails()) {
            return ['code' => 3001, 'message' => $validator->errors()->first(), 'data' => $validator->errors()];
        }
        $model = Category::find($request->id);
        $model->fill($request->all());
        if ($model->save()) {
            return ['code' => 200, 'message' =>'更新成功', 'data' => $model];
        } else {
            return ['code' => 5001, 'message' =>'更新失败'];
        }
    }

    // 删除目录
    public function destroy(Request $request)
    {
        $validator = Validator::make($request->all(),[
            'id' => 'required|integer'
        ]);
        if ($validator->fails()) {
            return ['code' => 3001, 'message' => $validator->errors()->first(), 'data' => $validator->errors()];
        }
        $category = Category::findOrFail($request->id);
        // 检查是否有子类
        if ($category->children()->count() > 0) {
            return ['code'=> 4001,'message' => '不能删除有子类的父类，请先删除子类'];
        }
        $category->delete();
        return ['code'=> 200,'message' => '删除成功'];
    }

}
