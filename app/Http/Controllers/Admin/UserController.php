<?php

namespace App\Http\Controllers\Admin;

use App\Models\AdminUser;
use App\Models\School;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use App\Http\Controllers\Controller;
use App\Services\Common\OfficeService;
use Tymon\JWTAuth\Exceptions\JWTException;
use Tymon\JWTAuth\Facades\JWTAuth;

class UserController extends Controller
{
    /**
     * 上传 Excel 文件并导入用户数据到数据库中
     * 支持设置学校ID和账号有效期
     * @param Request $request
     * @return array
     */
    public function import(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|mimes:xlsx,xls',
            'school_id' => 'required|integer|exists:school,id',
            'validity_days' => 'required|integer|min:1',
        ]);
        if ($validator->fails()) {
            return ['code' => 3001, 'message' => $validator->errors()->first(), 'data' => $validator->errors()];
        }
        $file = $request->file('file');
        $schoolId = $request->school_id;
        $validityDays = $request->validity_days;
        $expireTime = now()->addDays($validityDays);
        $ext = $file->getClientOriginalExtension();
        $realPath = $file->getRealPath();
        $filename = 'excel/importUsers.' . $ext;
        $bool = Storage::disk('public')->put($filename, file_get_contents($realPath));
        if (!$bool) {
            return ['code' => 3001, 'message' => '文件上传失败'];
        }
        $filePath = storage_path('app/public/' . $filename);
        try {
            $sheet = OfficeService::importExecl($filePath);
            Log::info('Excel 导入数据结构: ' . json_encode(array_keys($sheet)));
            if (!empty($sheet) && count($sheet) > 0) {
                $firstRow = reset($sheet);
                Log::info('第一行数据结构: ' . json_encode(array_keys($firstRow)));
            }

            if (empty($sheet) || count($sheet) < 2) {
                @unlink($filePath);
                return ['code'=>3001,'message' => 'Excel 文件格式不正确，至少需要包含表头和一行数据'];
            }
        } catch (\Exception $e) {
            @unlink($filePath);
            Log::error('Excel文件读取失败: ' . $e->getMessage());
            return ['code'=>3001,'message' => 'Excel 文件读取失败，请确保文件格式正确: ' . $e->getMessage()];
        }
        $usernameColumn = null;
        $passwordColumn = null;
        foreach ($sheet as $rowIndex => $row) {
            foreach ($row as $column => $value) {
                if (trim($value) === '用户名') {
                    $usernameColumn = $column;
                }
                if (trim($value) === '密码') {
                    $passwordColumn = $column;
                }
            }
            if ($usernameColumn && $passwordColumn) {
                break;
            }
        }
        if (!$usernameColumn || !$passwordColumn) {
            @unlink($filePath);
            return ['code'=>3001,'message' => 'Excel 表头中缺少必需的列（用户名、密码）'];
        }
        $successCount = 0;
        $failedCount = 0;
        $failedUsers = [];
        foreach ($sheet as $rowIndex => $row) {
            if ($rowIndex == 0 || $rowIndex == 1) {
                continue;
            }
            if (!isset($row[$usernameColumn]) || !isset($row[$passwordColumn])) {
                continue;
            }
            $username = trim($row[$usernameColumn]);
            $password = trim($row[$passwordColumn]);
            if ($username === '' || $password === '') {
                continue;
            }
            try {
                User::create([
                    'username' => $username,
                    'password' => Hash::make($password),
                    'status' => 1,
                    'school_id' => $schoolId,
                    'expire_time' => $expireTime,
                ]);
                $successCount++;
            } catch (\Exception $e) {
                $failedCount++;
                $failedUsers[] = $username;
                Log::error("用户【{$username}】导入失败，错误信息：" . $e->getMessage());
            }
        }
        @unlink($filePath);
        return [
            'code' => 200,
            'message' => "用户数据导入成功，共导入 {$successCount} 条记录" . ($failedCount > 0 ? "，失败 {$failedCount} 条" : ""),
            'data' => [
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'failed_users' => $failedUsers
            ]
        ];
    }


    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return ['code' => 3001, 'message' => $validator->errors()->first(), 'data' => $validator->errors()];
        }
        $data = $validator->validated();

        if (!AdminUser::validateCredentials($data['username'], $data['password'])) {
            return ['code' => 3002, 'message' => '用户名或密码不正确'];
        }
        $user = AdminUser::getAdminUser();
        try {
            $token = JWTAuth::fromUser($user);
            if (!$token) {
                return ['code' => 3003, 'message' => '无法生成token'];
            }
        } catch (JWTException $e) {
            return ['code' => 500, 'message' => '生成令牌时出错: ' . $e->getMessage()];
        }
        return [
            'code' => 200,
            'message' => '登录成功',
            'data' => [
                'username' => $user->username,
                'id' => $user->id,
            ],
            'token' => $token
        ];
    }

    public function index(Request $request)
    {
//        $model = User::query()->orderByDesc('id');
        $model = User::with('school')->orderByDesc('id');
        if ($request->username) {
            $model->where('username', 'like', "%{$request->username}%");
        }
        if ($request->school_id) {
            $model->where('school_id',$request->school_id);
        }
        if (isset($request->page) && $request->page <= 0) {
            $rows = $model->limit(15);
        } else {
            $rows = $model->paginate();
        }

        return ['code' => 200, 'message' => __('common.Success'), 'data' => $rows];
    }
}
