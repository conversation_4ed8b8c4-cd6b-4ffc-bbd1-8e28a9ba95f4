<?php

namespace App\Http\Controllers;

use App\Models\School;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;
use <PERSON><PERSON>\JWTAuth\Exceptions\JWTException;

class AuthController extends Controller
{
    /**
     * 登录并返回 JWT token
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        // 如果验证失败，返回错误信息
        if ($validator->fails()) {
            return ['code' => 3001, 'message' => $validator->errors()->first(), 'data' => $validator->errors()];
        }

        // 获取验证后的数据
        $data = $validator->validated();

        // 查找用户
        $user = User::where('username', $data['username'])->first();

        // 用户不存在或密码不正确
        if (!$user || !Hash::check($data['password'], $user->password)) {
            return ['code' => 3002, 'message' => 'The user name or password is incorrect'];
        }

        // 检查账号状态
        if ($user->status !== 1) {
            return ['code' => 3004, 'message' => 'Account is disabled'];
        }

        // 检查账号是否过期
        if ($user->expire_time && now()->gt($user->expire_time)) {
            return ['code' => 3005, 'message' => 'Account has expired'];
        }

        // 如果用户之前有token，先让之前的token失效
        if ($user->token) {
            try {
                // 使之前的token失效
                JWTAuth::setToken($user->token)->invalidate();
            } catch (\Exception $e) {
                // 如果token已经失效，忽略异常
            }
        }

        // 生成新的 JWT token
        try {
            $token = JWTAuth::attempt($data);
            if (!$token) {
                return ['code' => 3003, 'message' => 'Unable to generate token'];
            }
        } catch (JWTException $e) {
            return ['code' => 500, 'message' => 'Error generating Token'];
        }

        // 更新用户的最后登录时间和token
        $user->last_login = now();
        $user->token = $token;
        $user->save();

        // 获取学校名称
        $schoolName = null;
        if ($user->school_id) {
            $school = School::find($user->school_id);
            $schoolName = $school ? $school->name : null;
        }

        // 返回生成的 token 和用户信息
        return [
            'code' => 200,
            'message' => 'Login successful',
            'data' => [
                'id' => $user->id,
                'username' => $user->username,
                'status' => $user->status,
                'last_login' => $user->last_login,
                'expire_time' => $user->expire_time,
                'school' => $schoolName
            ],
            'token' => $token
        ];
    }


    public function logout(Request $request)
    {
        try {
            $user = $request->user();
            if ($user) {
                $user->token = null;
                $user->save();
            }
            JWTAuth::parseToken()->invalidate();
            return ['code' => 200, 'message' => 'Successfully logged out'];
        } catch (JWTException $e) {
            return ['code' => 500, 'message' => 'Failed to log out'];
        }
    }

    /**
     * 客户端修改密码
     * @param Request $request
     * @return array
     */
    public function updatePassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'old_password' => 'required|string|min:6',
            'new_password' => 'required|string|min:6|different:old_password',
            'confirm_password' => 'required|string|same:new_password'
        ]);

        if ($validator->fails()) {
            return ['code' => 3001, 'message' => $validator->errors()->first(), 'data' => $validator->errors()];
        }
        $user = $request->user();
        if (!Hash::check($request->old_password, $user->password)) {
            return ['code' => 3002, 'message' => 'Old password is incorrect'];
        }
        try {
            $user->password = Hash::make($request->new_password);
            $user->save();
            if ($user->token) {
                JWTAuth::setToken($user->token)->invalidate();
                $user->token = null;
                $user->save();
            }
            return [
                'code' => 200,
                'message' => 'Password updated successfully. Please login again.'
            ];
        } catch (\Exception $e) {
            return ['code' => 5001, 'message' => 'Failed to update password'];
        }
    }
}


