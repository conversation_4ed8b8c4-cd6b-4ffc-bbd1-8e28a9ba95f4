<?php

namespace App\Imports;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class UsersImport implements ToModel, WithHeadingRow, WithChunkReading
{
    /**
     * @param array $row
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        if (empty($row['用户名']) || empty($row['密码'])) {
            return null;
        }

        return new User([
            'username' => trim($row['用户名']),
            'password' => Hash::make(trim($row['密码'])),
            'status'   => 1,  // 默认状态：正常
        ]);
    }

    /**
     * 分块读取，每次读取2000条数据
     * @return int
     */
    public function chunkSize(): int
    {
        return 2000;
    }
}
