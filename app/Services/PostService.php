<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use App\Models\Post;
use App\Models\User\UserCollect;
use App\Models\User\UserViewHistory;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Elasticsearch\ClientBuilder;
use Illuminate\Support\Facades\Cache;

class PostService
{

    const CACHE_TAGS = ['dynamic', 'post'];
    const CACHE_CATEGORY_KEY = 'post:category';
    const CACHE_POST_LIST_KEY = 'post:list';
    const CACHE_POST_HOME_DATA_KEY = 'post:home';

    public static function qeury($model, Request $request)
    {
        if ($request->category_id)
        {
            $model->leftJoin('category_post', function ($query) {
                $query->on('post.id', '=', 'category_post.post_id');
            });
        }
        $model->select(PostService::selectFields());
        $model->where('post.post_status', 1);
        if ($request->orderby === 'favorite')
        {
            $model->orderByDesc('post.post_favorites')->orderByDesc('post.id');
        } elseif ($request->orderby === 'hits')
        {
            $model->orderByDesc('post.post_hits')->orderByDesc('post.id');
        } else
        {
            if ($request->category_id)
            {
                $model->orderBy('category_post.sort');
            }
            $model->orderBy('post.id');
        }
        if ($request->post_type)
        {
            $model->where('post.post_type', $request->post_type);
        }
        if ($request->category_id)
        {
            $categoryModel = Category::find($request->category_id);
            if ($categoryModel->parent_id == 0)
            {
                $childrenIds = self::getChildrenCategories($request->category_id);
                $model->whereIn('category_post.category_id', $childrenIds);
            } else
            {
                $model->where('category_post.category_id', $request->category_id);
            }
            $model->addSelect('category_post.sort');
        }
        if ($request->cost_attribute)
        {
            $model->where('post.cost_attribute', $request->cost_attribute);
        }
        if ($request->is_top)
        {
            $model->where('post.is_top', $request->is_top);
        }
        if ($request->recommended)
        {
            $model->where('post.recommended', $request->recommended);
        }
        return $model;
    }

    /**
     * ElasticSearch
     * @param Request $request
     * @param type $page_size
     * @param type $onlySource 仅返回 _source 字段数据
     * @param type $includeFields _source仅包含字段
     * @return type
     */
    public static function elasticSearch(Request $request, $page_size = 15, $onlySource = false, $includeFields = [])
    {
        $post = new Post();
        $page = intval($request->page);
        if ($page < 1)
        {
            $page = 1;
        }
        $offset = ($page - 1) * $page_size;

        $es_index = config('scout.elasticsearch.index');
        $es_type = $post->searchableAs();
        $option = [
            'from' => $offset, //显示应该跳过的初始结果数量，默认是 0; 偏移量, 相当于 mysql limit offset
            'size' => $page_size, //显示应该返回的结果数量，默认是 10
            '_source' => [
                'includes' => empty($includeFields) ? PostService::selectFieldsForElasticSearch() : $includeFields, //返回字段
                'excludes' => [], //排除返回字段
            ],
            'query' => [
                'bool' => [
                    //should: `或的关系`，must: `并且的关系`
                    'must' => [
                        //多字段模糊匹配
                        'multi_match' => [
                            'query' => $request->keywords,
                            'fields' => ['post_title'],
                        ],
                    ],
                    //过滤
                    'filter' => [
//                        [
//                            //过滤掉`数字模型`
//                            'terms' => ['post_type' => $post->getPostTypes([$post::POST_TYPE_DMMODEL])]
//                        ],
                        [
                            //过滤掉`未发布`
                            'term' => ['post_status' => 1]
                        ],
                    ],
                ],
            ],
            //排序
            'sort' => [
            //'id' => ['order' => 'asc']
            ],
            //高亮显示
            'highlight' => [
                'pre_tags' => [
                    '<font color="red">'
                ],
                'post_tags' => [
                    '</font>'
                ],
                'fields' => [
                    'post_title' => new \stdClass(),
                    'post_excerpt' => new \stdClass(),
                ]
            ]
        ];
        if ($request->orderby === 'favorite')
        {
            $option = array_merge_recursive($option, [
                'sort' => ['post_favorites' => ['order' => 'desc']]
            ]);
        } elseif ($request->orderby === 'hits')
        {
            $option = array_merge_recursive($option, [
                'sort' => ['post_hits' => ['order' => 'desc']]
            ]);
        }
        if ($request->post_type)
        {
            if (is_array($request->post_type))
            {
                $option_where[]['terms']['post_type'] = $request->post_type;
            } else
            {
                $option_where[]['term']['post_type'] = $request->post_type;
            }
        } else
        {
            $option_where[]['terms']['post_type'] = $post->getPostTypes([$post::POST_TYPE_DMMODEL]);
        }
        if ($request->cost_attribute)
        {
            $option_where[]['term']['cost_attribute'] = $request->cost_attribute;
        }
        if ($request->is_top)
        {
            $option_where[]['term']['is_top'] = $request->is_top;
        }
        if ($request->recommended)
        {
            $option_where[]['term']['recommended'] = $request->recommended;
        }
        if ($request->updated_at_begin && $request->updated_at_end)
        {
            $option_where[]['range']['updated_at'] = [
                'gte' => $request->updated_at_begin,
                'lte' => Carbon::parse($request->updated_at_end)->endOfDay(),
                'include_lower' => true, //是否包含范围的左边界，默认是true
                'include_upper' => true, // 是否包含范围的右边界，默认是true
            ];
        }
        if (isset($option_where))
        {
            $option = array_merge_recursive($option, [
                'query' => [
                    'bool' => [
                        //过滤
                        'filter' => $option_where
                    ],
                ],
            ]);
        }
        $params = [
            'index' => $es_index,
            'type' => $es_type,
            'body' => $option,
        ];
        $client = ClientBuilder::create()->setHosts(config('scout.elasticsearch.hosts'))->build();
        $es_data = $client->search($params);
        if ($onlySource)
        {
            return collect($es_data['hits']['hits'])->pluck('_source')->toArray();
        }
        return $es_data;
    }

    /**
     * ElasticSearch
     * @param Request $request
     * @param type $page_size
     * @param type $onlySource 仅返回 _source 字段数据
     * @param type $includeFields _source仅包含字段
     * @return type
     */
    public static function elasticSearchForWechat(Request $request, $page_size = 15, $onlySource = false, $includeFields = [])
    {
        $post = new Post();
        $page = intval($request->page);
        if ($page < 1)
        {
            $page = 1;
        }
        $offset = ($page - 1) * $page_size;

        $es_index = config('scout.elasticsearch.index');
        $es_type = $post->searchableAs();
        $option = [
            'from' => $offset, //显示应该跳过的初始结果数量，默认是 0; 偏移量, 相当于 mysql limit offset
            'size' => $page_size, //显示应该返回的结果数量，默认是 10
            '_source' => [
                'includes' => empty($includeFields) ? PostService::selectFieldsForElasticSearch() : $includeFields, //返回字段
                'excludes' => [], //排除返回字段
            ],
            'query' => [
                'bool' => [
                    //should: `或的关系`，must: `并且的关系`
                    'must' => [
                        //多字段模糊匹配
                        'multi_match' => [
                            'query' => $request->keywords,
                            'fields' => ['post_title'],
                            'minimum_should_match' => '50%',
                        ],
                    ],
                    //过滤
                    'filter' => [
                        [
                            //过滤掉`数字模型`
                            'terms' => ['post_type' => $post->getPostTypes([$post::POST_TYPE_DMMODEL])]
                        ],
                        [
                            //过滤掉`未发布`
                            'term' => ['post_status' => 1]
                        ],
                    ],
                ],
            ],
            //排序
            'sort' => [
            //'id' => ['order' => 'asc']
            ],
            //高亮显示
            'highlight' => [
                'pre_tags' => [
                    '<font color="red">'
                ],
                'post_tags' => [
                    '</font>'
                ],
                'fields' => [
                    'post_title' => new \stdClass(),
                    'post_excerpt' => new \stdClass(),
                ]
            ]
        ];
        if ($request->orderby === 'favorite')
        {
            $option = array_merge_recursive($option, [
                'sort' => ['post_favorites' => ['order' => 'desc']]
            ]);
        } elseif ($request->orderby === 'hits')
        {
            $option = array_merge_recursive($option, [
                'sort' => ['post_hits' => ['order' => 'desc']]
            ]);
        }
        if ($request->post_type)
        {
            $option_where[]['term']['post_type'] = $request->post_type;
        } elseif ($request->cost_attribute)
        {
            $option_where[]['term']['cost_attribute'] = $request->cost_attribute;
        } elseif ($request->is_top)
        {
            $option_where[]['term']['is_top'] = $request->is_top;
        } elseif ($request->recommended)
        {
            $option_where[]['term']['recommended'] = $request->recommended;
        }
        if ($request->updated_at_begin && $request->updated_at_end)
        {
            $option_where[]['range']['updated_at'] = [
                'gte' => $request->updated_at_begin,
                'lte' => Carbon::parse($request->updated_at_end)->endOfDay(),
                'include_lower' => true, //是否包含范围的左边界，默认是true
                'include_upper' => true, // 是否包含范围的右边界，默认是true
            ];
        }
        if (isset($option_where))
        {
            $option = array_merge_recursive($option, [
                'query' => [
                    'bool' => [
                        //过滤
                        'filter' => $option_where
                    ],
                ],
            ]);
        }
        $params = [
            'index' => $es_index,
            'type' => $es_type,
            'body' => $option,
        ];
        $client = ClientBuilder::create()->setHosts(config('scout.elasticsearch.hosts'))->build();
        $es_data = $client->search($params);
        if ($onlySource)
        {
            return collect($es_data['hits']['hits'])->pluck('_source')->toArray();
        }
        return $es_data;
    }

    public static function selectFields()
    {
        return ['post.id', 'post.post_sn', 'post.post_type', 'post.post_title', 'post.cost_attribute', 'post.coin', 'post.post_keywords', 'post.post_excerpt', 'post.thumbnail', 'post.post_hits', 'post.post_favorites', 'post.post_like', 'post.comment_count', 'post.updated_at'];
    }

    public static function selectFieldsForElasticSearch()
    {
        return collect(self::selectFields())
                        ->map(function ($value) {
                            return str_replace('post.', '', $value);
                        })
                        ->toArray();
    }

    /**
     * ElasticSearch 扩展词 【暂时不用】
     * @param array $keywords
     * @return type
     */
    public static function updateCustomKeywords(array $keywords)
    {
        return 1; //***暂时不用
        $keywords = collect($keywords)->map(function ($value) {
            return trim($value);
        });
        $rows = DB::table('post_custom_keywords')->get()->pluck('k');
        $end_data = [];
        foreach ($keywords->diff($rows) as $kw)
        {
            $end_data[]['k'] = $kw;
        }
        return DB::table('post_custom_keywords')->insert($end_data);
    }

    /**
     * 是否已购买该资源
     * @param type $post_id
     * @return boolean
     */
    public static function isBought($post_id)
    {
        return true;
    }


    /**
     * 验证评论内容是否可以显示
     * @param type $content
     */
    public static function checkCommentContent($content)
    {
        //内容含有域名
        if (preg_match('#[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\.?#', $content))
        {
            return false;
        }
        //内容未通过微信接口检测
        //...
        return true;
    }




    /**
     * 获取封面图url
     * @param type $post_type
     * @param type $post_sn
     * @return type
     */
    public static function getThumbnailByPostSn($post_type, $post_sn)
    {
        $base_url = config('filesystems.disks.s3.posts_url');
        if ($post_type == Post::POST_TYPE_3DMODEL)
        {
            $base_url .= "/model/{$post_sn}/preview.png";
        } elseif ($post_type == Post::POST_TYPE_VIDEO)
        {
            $base_url .= "/video/{$post_sn}/preview.png";
        } elseif ($post_type == Post::POST_TYPE_GALLERY)
        {
            $base_url .= "/photo/{$post_sn}/preview.png";
        } elseif ($post_type == Post::POST_TYPE_PPT)
        {
            $base_url .= "/ppt/{$post_sn}/preview.png";
        } elseif ($post_type == Post::POST_TYPE_DMMODEL)
        {
            $base_url .= "/digitalModel/{$post_sn}/preview.png";
        } elseif ($post_type == Post::POST_TYPE_SECTION)
        {
            $base_url .= "/section/{$post_sn}/preview.png";
        } elseif ($post_type == 8)
        {
            $base_url .= "/digitalModelSmart/{$post_sn}/preview.png";
        }
        return $base_url;
    }



    /**
     * 获取资源表字段more属性数据
     * @param type $post_type
     * @param type $post_sn
     * @param type $ext
     * @return type
     */
    public static function getPostMoreDataByImport($post_type, $post_sn, $ext = [])
    {
        $base_url = config('filesystems.disks.s3.posts_url');
        if ($post_type == Post::POST_TYPE_3DMODEL)
        {
            $base_url .= "/model/{$post_sn}";
            $data['model'] = $base_url;
        } elseif ($post_type == Post::POST_TYPE_VIDEO)
        {
            $base_url .= "/video/{$post_sn}/{$post_sn}.mp4";
            $data['video'] = $base_url;
        } elseif ($post_type == Post::POST_TYPE_GALLERY)
        {
            if (empty($ext))
            {
                $data['photo'] = [];
            }
            foreach ($ext['photos'] as $photo)
            {
                $data['photo'][] = $base_url . "/photo/{$post_sn}/album/{$photo}.png";
            }
        } elseif ($post_type == Post::POST_TYPE_PPT)
        {
            $base_url .= "/ppt/{$post_sn}/{$post_sn}.pptx";
            $data['ppt'] = $base_url;
        } elseif ($post_type == Post::POST_TYPE_DMMODEL)
        {
            $base_url .= "/digitalModel/{$post_sn}/{$post_sn}.fbx";
            $data['digitalModel'] = $base_url;
        } elseif ($post_type == Post::POST_TYPE_SECTION)
        {
            $base_url .= "/section/{$post_sn}/files/";
            $data['_section'] = $base_url;
            $data['section_width'] = $ext['section_width'];
            $data['section_height'] = $ext['section_height'];
        }
        return $data ?? [];
    }

    /**
     * 获取上级分类（向上获取两级）
     * @param array $category_id
     * @return type
     */
    public static function getCategoryParentIds(array $category_id)
    {
        $category_id = collect($category_id)->values()->toArray();
        //获取上级分类id
        $parent_ids = Category::whereIn('id', $category_id)
                ->select('parent_id')
                ->get()
                ->pluck('parent_id')
                ->reject(function ($value) {
                    if ($value <= 0)
                    {
                        return true;
                    }
                })
                ->toArray();
        //获取上上级分类id
        $parent_parent_ids = Category::whereIn('id', $parent_ids)
                ->select('parent_id')
                ->get()
                ->pluck('parent_id')->reject(function ($value) {
                    if ($value <= 0)
                    {
                        return true;
                    }
                })
                ->toArray();
        return array_merge_recursive($category_id, $parent_ids, $parent_parent_ids);
    }

    /**
     * 获取子分类（向下获取二级）
     * @param type $category_id
     */
    public static function getChildrenCategories($category_id)
    {
        $rows = Category::where('parent_id', $category_id)->pluck('id')->toArray();
        $arr = [];
        foreach ($rows as $id)
        {
            $childRows = Category::where('parent_id', $id)->pluck('id')->toArray();
            $arr = array_merge_recursive($arr, $childRows);
        }
        return array_merge_recursive($arr, $rows);
//        $query = "WITH RECURSIVE category_tree AS (
//                  SELECT id FROM yjp_category WHERE id = ?
//                  UNION ALL
//                  SELECT c.id FROM yjp_category c
//                  INNER JOIN category_tree ct ON c.parent_id = ct.id
//              )
//              SELECT id FROM category_tree";
//
//        $rows = DB::select($query, [$category_id]);
//
//        return array_column($rows, 'id');
    }

    /**
     * 获取前端传过来的分层级的分类id
     *
     * @param array $category_ids
     */
    public static function getLastCategory(array $category_ids)
    {
        $arr = [];
        foreach ($category_ids as $row)
        {
            $arr[] = array_pop($row);
        }
        return $arr;
    }

    /**
     * 处理成前端可以解析的分层级的分类id
     *
     * @param array $category_ids
     */
    public static function handleLastCategory(array $category_ids)
    {
        $parent_ids = Category::whereIn('id', $category_ids)->select('id', 'parent_id')->get();
        $arr = [];
        foreach ($parent_ids as $row)
        {
            $arr[] = [$row->parent_id, $row->id];
        }
        return $arr;
    }

    /**
     * 处理前端传过来的分层级的分类id(导入excel)
     *
     * @param string $category_ids
     */
    public static function getLastCategoryForImport(string $category_ids)
    {
        $category_ids = str_replace('，', ',', $category_ids);
        $arr = explode(',', $category_ids);
        return collect($arr)
                        ->map(function ($value, $key) {
                            return (int) $value;
                        })
                        ->reject(function ($value) {
                            return intval($value) <= 0;
                        })
                        ->values()
                        ->all();
    }

    /**
     * 获取资源总数
     * @param type $category_id
     */
    public static function getPostCountByCategoryId($category_id)
    {
        $category_ids = self::getChildrenCategories($category_id);
        if ($category_ids)
        {
            $category_ids = array_merge_recursive($category_ids, [$category_id]);
        }
        return Post::where('post_status', 1)
                        ->whereHas('categories', function ($query)use ($category_ids) {
                            $query->whereIn('category_id', $category_ids);
                        })
                        ->count();
    }

    /**
     * 删除分类 【后台管理】
     * @param array $category_ids 前端传过来的分类id
     */
    public static function deleteCategory(array $category_ids)
    {

    }

    public static function getNextPreId($rows_id, $current_id)
    {
        if (count($rows_id) < 2)
        {
            $pre_id = $next_id = null;
        }
        $arr = array_flip($rows_id);
        $pos = $arr[$current_id] ?? null;
        if ($pos === null)
        {
            $pre_id = $next_id = null;
        }
        $pos_next_id = $pos + 1;
        $pos_pre_id = $pos - 1;
        $next_id = $rows_id[$pos_next_id] ?? null;
        $pre_id = $rows_id[$pos_pre_id] ?? null;

        $next_row = Post::find($next_id);
        $next_data = [
            'post_id' => $next_id,
            'post_title' => $next_row->post_title ?? null,
        ];
        $pre_row = Post::find($pre_id);
        $pre_data = [
            'post_id' => $pre_id,
            'post_title' => $pre_row->post_title ?? null,
        ];
        return ['next_data' => $next_data, 'pre_data' => $pre_data];
    }

    static public function putCacheCategory($data, $request)
    {
        $key = self::getCacheCategoryKeyName($request);
        Cache::tags(self::CACHE_TAGS)->put($key, $data, now()->tomorrow());
    }

    static public function getCacheCategoryKeyName($request)
    {
        $key = self::CACHE_CATEGORY_KEY;
        if ($request->type)
        {
            $key .= ":type:{$request->type}";
        }
        return $key;
    }

    static public function getCacheCategory($request)
    {
        $key = self::getCacheCategoryKeyName($request);
        return Cache::tags(self::CACHE_TAGS)->get($key);
    }

    static public function putCachePostList($data, $request)
    {
        $key = self::getCachePostListKeyName($request);
        Cache::tags(self::CACHE_TAGS)->put($key, $data, now()->tomorrow());
    }

    static public function getCachePostList($request)
    {
        $key = self::getCachePostListKeyName($request);
        return Cache::tags(self::CACHE_TAGS)->get($key);
    }

    static public function getCachePostListKeyName($request)
    {
        $page_size = $request->page_size ?: 15;
        $page = $request->page ?: 1;
        $key = self::CACHE_POST_LIST_KEY;
        $key .= ":page_size:{$page_size}";
        $key .= ":page:{$page}";
        if ($request->orderby)
        {
            $key .= ":{$request->orderby}";
        }
        if ($request->category_id)
        {
            $key .= ":{$request->category_id}";
        }
        if ($request->post_type)
        {
            $key .= ":{$request->post_type}";
        }
        if ($request->cost_attribute)
        {
            $key .= ":{$request->cost_attribute}";
        }
        if ($request->is_top)
        {
            $key .= ":{$request->is_top}";
        }
        if ($request->recommended)
        {
            $key .= ":{$request->recommended}";
        }

        return $key;
    }

    static public function putCachePostHome($data)
    {
        $key = self::CACHE_POST_HOME_DATA_KEY;
        Cache::tags(self::CACHE_TAGS)->put($key, $data, now()->tomorrow());
    }

    static public function getCachePostHome()
    {
        $key = self::CACHE_POST_HOME_DATA_KEY;
        return Cache::tags(self::CACHE_TAGS)->get($key);
    }

}
