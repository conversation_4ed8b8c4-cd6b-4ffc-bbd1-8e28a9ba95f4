<?php

namespace App\Services\Common;

/**
 * office service
 *
 * <AUTHOR>
 */
class OfficeService
{

    /**
     * 使用PHPEXECL导入
     *
     * @param string $file 文件地址
     * @param int $columnCnt 列数(传0则自动获取最大列)
     * @param int $row 行数(传0则自动获取最大行)
     * @param int $sheet 工作表sheet(传0则获取第一个sheet)
     * @param array $options 操作选项
     *                          array mergeCells 合并单元格数组
     *                          array formula    公式数组
     *                          array format     单元格格式数组
     *
     * @return array
     * @throws Exception
     */
    public static function importExecl(string $file = '', int $columnCnt = 0, int $row = 0, int $sheet = 0, &$options = [])
    {
        try {
            /* 转码 */
            //$file = iconv("gb2312","utf-8", $file);
            if (empty($file) OR!file_exists($file))
            {
                throw new \Exception('文件不存在!');
            }
            /** @var Xlsx $objRead */
            $objRead = \PhpOffice\PhpSpreadsheet\IOFactory::createReader('Xlsx');

            if (!$objRead->canRead($file))
            {
                /** @var Xls $objRead */
                $objRead = \PhpOffice\PhpSpreadsheet\IOFactory::createReader('Xls');

                if (!$objRead->canRead($file))
                {
                    throw new \Exception('只支持导入Excel文件！');
                }
            }

            /* 如果不需要获取特殊操作，则只读内容，可以大幅度提升读取Excel效率 */
            empty($options) && $objRead->setReadDataOnly(true);
            /* 建立excel对象 */
            $obj = $objRead->load($file);
            /* 获取指定的sheet表 */
            $currSheet = $obj->getSheet($sheet);
            if (isset($options['mergeCells']))
            {
                /* 读取合并行列 */
                $options['mergeCells'] = $currSheet->getMergeCells();
            }
            if (0 == $columnCnt)
            {
                /* 取得最大的列号 */
                $columnH = $currSheet->getHighestColumn();
                /* 兼容原逻辑，循环时使用的是小于等于 */
                $columnCnt = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($columnH);
            }
            /* 获取总行数 */
            $rowCnt = $row > 0 ? $row : $currSheet->getHighestRow();
            $data = [];
            /* 读取内容 */
            for ($_row = 0; $_row <= $rowCnt; $_row++)
            {
                $isNull = true;
                for ($_column = 1; $_column <= $columnCnt; $_column++)
                {
                    $cellName = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($_column);
                    $cellId = $cellName . $_row;
                    $cell = $currSheet->getCell($cellId);
                    if (isset($options['format']))
                    {
                        /* 获取格式 */
                        $format = $cell->getStyle()->getNumberFormat()->getFormatCode();
                        /* 记录格式 */
                        $options['format'][$_row][$cellName] = $format;
                    }
                    if (isset($options['formula']))
                    {
                        /* 获取公式，公式均为=号开头数据 */
                        $formula = $currSheet->getCell($cellId)->getValue();

                        if (0 === strpos($formula, '='))
                        {
                            $options['formula'][$cellName . $_row] = $formula;
                        }
                    }
                    if (isset($format) && 'm/d/yyyy' == $format)
                    {
                        /* 日期格式翻转处理 */
                        $cell->getStyle()->getNumberFormat()->setFormatCode('yyyy/mm/dd');
                    }
                    $data[$_row][$cellName] = trim($currSheet->getCell($cellId)->getFormattedValue());

                    if (!empty($data[$_row][$cellName]))
                    {
                        $isNull = false;
                    }
                }
                /* 判断是否整行数据为空，是的话删除该行数据 */
                if ($isNull)
                {
                    unset($data[$_row]);
                }
            }
            return $data;
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 导出excel
     * @param type $request
     * @param type $data
     * @param type $filename
     * @param type $title
     */
    function exportExcel($request, $data, $filename, $title = '', $allow_origin = true)
    {
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        if ($title)
        {
            $sheet->setTitle($title);
        } else
        {
            $sheet->setTitle($filename);
        }
        $keys = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        if ($data)
        {
            foreach ($data as $k => $item)
            {
                foreach ($item as $item_key => $value)
                {
                    $cell = $item_key > 26 ? (str_pad($keys[$item_key % 26], ceil($item_key / 26), $keys[$item_key % 26])) : $keys[$item_key];
                    $sheet->setCellValue($cell . ($k + 1), $value);
                }
            }
        }
        $origin = $request->header('origin') ? $request->header('origin') : '';
        if ($allow_origin)
        {
            header('Access-Control-Allow-Origin:' . $origin);
            header('Access-Control-Allow-Credentials:true');
            header('Pragma:public');
            header('Content-Type:application/x-msexecl;name=' . urlencode($filename) . '.xlsx');
            header('Content-Disposition:inline;filename=' . $filename . '.xlsx');
        }

        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save('php://output');
    }

    /**
     * word文档转换成文本
     * @param type $source
     * @return type
     */
    static public function docx2text($source)
    {
        $phpWord = \PhpOffice\PhpWord\IOFactory::load($source);
        $html = '';
        foreach ($phpWord->getSections() as $section)
        {
            foreach ($section->getElements() as $ele1)
            {
                if ($ele1 instanceof \PhpOffice\PhpWord\Element\TextRun)
                {
                    foreach ($ele1->getElements() as $ele2)
                    {
                        if ($ele2 instanceof \PhpOffice\PhpWord\Element\Text)
                        {
                            $html .= mb_convert_encoding($ele2->getText(), 'GBK', 'UTF-8');
                        } elseif ($ele2 instanceof \PhpOffice\PhpWord\Element\Image)
                        {
                            $url = \App\Services\UploadService::toAliOss(file_get_contents($ele2->getSource()));
                            $html .= '<img src="' . $url . '">';
                        } else
                        {

                        }
                    }
                    $html .= "\n";
                } elseif ($ele1 instanceof \PhpOffice\PhpWord\Element\TextBreak)
                {
                    //段落
                } elseif ($ele1 instanceof \PhpOffice\PhpWord\Element\PreserveText)
                {
                    //超链接
                    //$html .= join(' ', $ele1->getText());
                    //$html .= "\n";
                } elseif ($ele1 instanceof \PhpOffice\PhpWord\Element\Table)
                {

                } elseif ($ele1 instanceof \PhpOffice\PhpWord\Element\Link)
                {
                    //$html .= $ele1->getText();
                    //$html .= "\n";
                } elseif ($ele1 instanceof \PhpOffice\PhpWord\Element\OLEObject)
                {
                    //公式
                } else
                {

                }
            }
        }
        return mb_convert_encoding($html, 'UTF-8', 'GBK');
    }

    /**
     * word文档转换成html
     * @param type $source
     * @return type
     */
    function docx2html($source)
    {
        $phpWord = \PhpOffice\PhpWord\IOFactory::load($source);
        $html = '';
        foreach ($phpWord->getSections() as $section)
        {
            foreach ($section->getElements() as $ele1)
            {
                $paragraphStyle = $ele1->getParagraphStyle();
                if ($paragraphStyle)
                {
                    $html .= '<p style="text-align:' . $paragraphStyle->getAlignment() . ';text-indent:20px;">';
                } else
                {
                    $html .= '<p>';
                }
                if ($ele1 instanceof \PhpOffice\PhpWord\Element\TextRun)
                {
                    foreach ($ele1->getElements() as $ele2)
                    {
                        if ($ele2 instanceof \PhpOffice\PhpWord\Element\Text)
                        {
                            $style = $ele2->getFontStyle();
                            $fontFamily = mb_convert_encoding($style->getName(), 'GBK', 'UTF-8');
                            $fontSize = $style->getSize();
                            $isBold = $style->isBold();
                            $styleString = '';
                            $fontFamily && $styleString .= "font-family:{$fontFamily};";
                            $fontSize && $styleString .= "font-size:{$fontSize}px;";
                            $isBold && $styleString .= "font-weight:bold;";
                            $html .= sprintf('<span style="%s">%s</span>',
                                    $styleString,
                                    mb_convert_encoding($ele2->getText(), 'GBK', 'UTF-8')
                            );
                        } elseif ($ele2 instanceof \PhpOffice\PhpWord\Element\Image)
                        {
                            //$imageSrc = 'images/' . md5($ele2->getSource()) . '.' . $ele2->getImageExtension();
                            $imageData = $ele2->getImageStringData(true);
                            $imageData = 'data:' . $ele2->getImageType() . ';base64,' . $imageData;
                            //file_put_contents($imageSrc, base64_decode($imageData));
                            $html .= '<img src="' . $imageData . '" style="width:100%;height:auto">';
                        }
                    }
                }
                $html .= '</p>';
            }
        }

        return mb_convert_encoding($html, 'UTF-8', 'GBK');
    }

}
