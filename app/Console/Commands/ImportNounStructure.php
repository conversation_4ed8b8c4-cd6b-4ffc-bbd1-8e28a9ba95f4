<?php

namespace App\Console\Commands;

use App\Http\Controllers\Admin\NounStructureController;
use Illuminate\Console\Command;

class ImportNounStructure extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:noun-structure';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '导入结构名词数据（先导入结构名词1.xlsx，再用结构名词3.xlsx补充）';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始导入结构名词数据...');

        // 检查文件是否存在
        $file1 = storage_path('app/public/excel/结构名词1.xlsx');
        $file3 = storage_path('app/public/excel/结构名词3.xlsx');

        if (!file_exists($file1)) {
            $this->error('结构名词1.xlsx 文件不存在，请将文件放置在 storage/app/public/excel/ 目录下');
            return 1;
        }

        if (!file_exists($file3)) {
            $this->error('结构名词3.xlsx 文件不存在，请将文件放置在 storage/app/public/excel/ 目录下');
            return 1;
        }

        // 创建控制器实例并执行导入
        $controller = new NounStructureController();
        $result = $controller->importNounStructure();

        if ($result['code'] === 200) {
            $this->info($result['message']);

            // 显示详细结果
            if (isset($result['data']['first_excel_result'])) {
                $firstResult = $result['data']['first_excel_result']['data'];
                $this->line("结构名词1.xlsx: 成功 {$firstResult['success_count']} 条，失败 {$firstResult['failed_count']} 条");
            }

            if (isset($result['data']['supplement_result'])) {
                $supplementResult = $result['data']['supplement_result']['data'];
                $this->line("结构名词3.xlsx补充: 匹配 {$supplementResult['matched_count']} 条，更新 {$supplementResult['updated_count']} 条");
            }

            if (isset($result['data']['missing_data_result'])) {
                $missingResult = $result['data']['missing_data_result']['data'];
                $this->line("结构名词3.xlsx新增: 添加 {$missingResult['added_count']} 条，跳过 {$missingResult['skipped_count']} 条");
            }

            return 0;
        } else {
            $this->error($result['message']);
            return 1;
        }
    }
}
