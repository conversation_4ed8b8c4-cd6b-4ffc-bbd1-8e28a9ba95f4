<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\LoginRecords;
use Carbon\Carbon;

class UpdateLoginStatus extends Command
{
    protected $signature = 'login:update-status';
    protected $description = 'Update login status based on login_time exceeding 1 hour';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        // 当前时间
        $now = Carbon::now();
        // 一小时前的时间
        $oneHourAgo = $now->subHour();

        // 更新超过一小时的记录，将 status 置为 0
        $updated = LoginRecords::where('status', 1)
            ->where('login_time', '<=', $oneHourAgo)
            ->update(['status' => 0, 'updated_at' => $now]);

        // 输出日志或信息（可选）
        $this->info("Updated {$updated} records' status to offline.");
    }
}
