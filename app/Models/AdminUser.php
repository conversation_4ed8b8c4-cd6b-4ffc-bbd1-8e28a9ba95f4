<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Support\Facades\Hash;
use Tymon\JWTAuth\Contracts\JWTSubject;

class AdminUser extends Authenticatable implements JWTSubject
{
    // 默认的用户名
//    protected static $adminUsername = 'sysAdm!n_428';
//    // 默认的明文密码
//    protected static $adminPassword = 'Jb6#qF!9W@z$eP*2';
    protected static $adminUsername = 'admin';
    // 默认的明文密码
    protected static $adminPassword = '123456';

    // 生成加密后的密码并存储
    protected static $adminCredentials = [];

    public static function initializeAdminCredentials()
    {
        self::$adminCredentials = [
            'username' => self::$adminUsername,
            'password' => Hash::make(self::$adminPassword),  // 加密密码
        ];
    }
    // 硬编码的管理员用户信息
//    protected static $adminCredentials = [
//        'username' => 'admin',
//        // 存储的是Hash加密后的密码 - 这里假设密码是"123456"
//        'password' => '123456'
//    ];

    // 验证用户凭证
    public static function validateCredentials($username, $password)
    {
        // 确保凭证已经初始化
        if (empty(self::$adminCredentials)) {
            self::initializeAdminCredentials();  // 初始化凭证
        }

        return $username === self::$adminCredentials['username'] &&
            Hash::check($password, self::$adminCredentials['password']); // 使用 Hash::check 验证密码
    }

    // 获取管理员用户实例
    public static function getAdminUser()
    {
        // 确保凭证已经初始化
        if (empty(self::$adminCredentials)) {
            self::initializeAdminCredentials();  // 初始化凭证
        }
        $user = new self();
        $user->username = self::$adminCredentials['username'];
        $user->id = 1; // 给个固定ID
        return $user;
    }

    // 必须实现的JWT方法
    public function getJWTIdentifier()
    {
        return 1; // 硬编码ID
    }

    public function getJWTCustomClaims()
    {
        return [
            'username' => $this->username,
            'role' => 'admin'
        ];
    }

    protected function serializeDate(\DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }
}
