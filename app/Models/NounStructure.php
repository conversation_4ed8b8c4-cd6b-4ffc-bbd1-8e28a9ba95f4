<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NounStructure extends Model
{
    use HasFactory;

    /**
     * 指定表名
     */
    protected $table = 'noun_structure';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'key',
        'noun_ch',
        'note_ch',
        'noun_en',
        'note_en',
    ];

    /**
     * 需要转换为日期的属性
     */
    protected $dates = [
        'created_at',
        'updated_at'
    ];

    /**
     * 自定义日期格式
     */
    protected function serializeDate(\DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    /**
     * 检查指定字段是否为空
     */
    public function hasEmptyFields()
    {
        return empty($this->noun_ch) || empty($this->note_ch) || empty($this->noun_en) || empty($this->note_en);
    }

    /**
     * 获取空字段列表
     */
    public function getEmptyFields()
    {
        $emptyFields = [];
        if (empty($this->noun_ch)) $emptyFields[] = 'noun_ch';
        if (empty($this->note_ch)) $emptyFields[] = 'note_ch';
        if (empty($this->noun_en)) $emptyFields[] = 'noun_en';
        if (empty($this->note_en)) $emptyFields[] = 'note_en';
        return $emptyFields;
    }
}
