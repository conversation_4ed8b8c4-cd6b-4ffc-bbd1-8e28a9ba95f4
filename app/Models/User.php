<?php

namespace App\Models;

use Ty<PERSON>\JWTAuth\Contracts\JWTSubject;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\SoftDeletes;

class User extends Authenticatable implements JWTSubject
{
    use HasFactory, Notifiable, SoftDeletes;

    protected $table = 'users';  // 设置表名

    protected $fillable = [
        'username',
        'password',
        'status',
        'last_login',
        'school_id',
        'expire_time',
        'token',
        'created_at'
    ];

    protected $dates = [
        'last_login',
        'expire_time',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    protected $hidden = ['password', 'remember_token'];

    /**
     * 与学校的关联关系
     */
    public function schoolRelation()
    {
        return $this->belongsTo(School::class, 'school_id', 'id');
    }

    /**
     * 自定义日期格式
     */
    protected function serializeDate(\DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    /**
     * 获取 JWT 唯一标识符
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * 获取 JWT 的自定义声明
     */
    public function getJWTCustomClaims()
    {
        return [];
    }

    public function school()
    {
        return $this->belongsTo(School::class, 'school_id', 'id');
    }
}

