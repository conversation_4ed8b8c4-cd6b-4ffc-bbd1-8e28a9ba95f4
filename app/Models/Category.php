<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Category extends Model
{
    use HasFactory;
    use SoftDeletes;

    // 指定表名
    protected $table = 'category';

    // 可填充字段
    protected $fillable = [
        'parent_id',
        'name',
        'description',
        'status',
        'sort',
        'code',
    ];

    // 定义与父目录的关系
    public function parent()
    {
        return $this->belongsTo(Category::class, 'parent_id');
    }

    // 定义与子目录的关系
    public function children()
    {
        return $this->hasMany(Category::class, 'parent_id')->with('children');
    }

    protected function serializeDate(\DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    public function posts()
    {
        return $this->belongsToMany(Post::class, 'category_post', 'category_code', 'post_id');
    }
}
