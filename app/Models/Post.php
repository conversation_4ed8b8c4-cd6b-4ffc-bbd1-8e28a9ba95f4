<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

/**
 * 资源 [模型,视频,图集,ppt,文章]
 */
class Post extends Model
{

    use \Illuminate\Database\Eloquent\SoftDeletes;

    protected $table = 'post';
    protected $guarded = [];
    protected $casts = ['more' => 'json'];
    protected $hidden = ['deleted_at'];

    protected function serializeDate(\DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }
    /**
     * 实物模型
     */
    const POST_TYPE_3DMODEL = 1;

    /**
     * 视频
     */
    const POST_TYPE_VIDEO = 2;

    /**
     * 图集
     */
    const POST_TYPE_GALLERY = 3;

    /**
     * PPT
     */
    const POST_TYPE_PPT = 4;

    /**
     * 文章/咨询
     */
    const POST_TYPE_ARTICLE = 5;

    /**
     * 数字模型
     */
    const POST_TYPE_DMMODEL = 6;

    /**
     * 切片
     */
    const POST_TYPE_SECTION = 7;

    /**
     * 考试试题
     */
    const POST_TYPE_EXAM = 8;

    /**
     * Validator rules
     * @param type $on
     * @return type
     */
    public function rules(Request $request = null)
    {
        $rules = [
            'post_type' => ['required', 'integer', Rule::in(self::getPostTypes())],
            'post_sn' => ['required', 'string', 'max:50'],
            'post_status' => ['integer', 'in:1,2'],
            'post_title' => ['required', 'string', 'max:100'],
            'thumbnail' => ['string', 'max:255'],
            'post_content' => ['if:post_type,5', 'string', 'max:65535'],
            'more' => ['array'],
            'sort' => ['integer'],
            'created_at' => ['date'],
            'updated_at' => ['date'],
            'deleted_at' => ['date'],
        ];
        return $rules;
    }

    /**
     * Validator messages
     * @return type
     */
    public function messages()
    {
        return [
            'more.photo.min' => '图集至少1一张。',
            'more.model.required_if' => ':attribute 不能为空。',
            'more.video.required_if' => ':attribute 不能为空。',
            'more.photo.required_if' => ':attribute 不能为空。',
            'more.ppt.required_if' => ':attribute 不能为空。',
            'category_id' => '请选择所属分类。',
        ];
    }

    /**
     * Validator customAttributes
     * @return type
     */
    public function customAttributes()
    {
        return [
            'id' => '资源id',
            'post_type' => '资源类型',
            'post_sn' => '资源编号',
            'post_status' => '状态',
            'post_title' => '标题',
            'thumbnail' => '缩略图',
            'post_content' => '文章内容',
            'more' => '扩展属性',
            'sort' => '自定义排序',
            'created_at' => '创建时间',
            'updated_at' => '更新时间',
            'deleted_at' => '删除时间',
            'more.model' => '3d模型地址',
            'more.video' => '视频地址',
            'more.photo' => '图集',
            'more.ppt' => 'PPT地址',
        ];
    }

    /**
     * Filter Request Attributes and Retain only customAttributes
     * @param array $data
     * @param type $append_except
     * @return type
     */
    public function onlyCustomAttributes(array $data, array $append_except = [])
    {
        $except = array_merge_recursive($append_except, ['id', 'created_at', 'updated_at', 'deleted_at']);
        $attributes = $this->customAttributes();
        return collect($data)->except($except)->only(array_keys($attributes))->except($except)->toArray();
    }

    /**
     * Fill the model with an array of attributes.
     * @param type $attributes
     * @return $this
     */
    public function fill($attributes)
    {
        $onlyCustomAttributes = $this->onlyCustomAttributes($attributes);
        parent::fill($onlyCustomAttributes);
        return $this;
    }

    /**
     * 资源类型
     * @param array $except 排除的值
     * @param array $only 仅获取的值
     * @return type
     */
    public static function getPostTypes(array $except = [], array $only = [])
    {
        $data = collect([
            self::POST_TYPE_3DMODEL,
            self::POST_TYPE_VIDEO,
            self::POST_TYPE_GALLERY,
            self::POST_TYPE_PPT,
            self::POST_TYPE_ARTICLE,
            self::POST_TYPE_DMMODEL,
            self::POST_TYPE_SECTION,
            self::POST_TYPE_EXAM,

        ]);
        if (!empty($except)) {
            $data = $data->filter(function($value)use($except) {
                if (in_array($value, $except)) {
                    return false;
                }
                return true;
            });
        }
        if (!empty($only)) {
            $data = $data->filter(function($value)use($only) {
                if (in_array($value, $only)) {
                    return true;
                }
                return false;
            });
        }
        return $data->values()->toArray();
    }

    /**
     * 配置模型索引 「scout 搜索」
     * 默认情况下，每个模型都会被持久化到与模型的 「表」 名（通常是模型名称的复数形式）相匹配的索引。
     * @return string
     */
    public function searchableAs(): string
    {
        return $this->getTable() . '_type';
    }

    /**
     * 配置可搜索数据 「scout 搜索」
     * 默认情况下，模型以完整的 toArray 格式持久化到搜索索引。
     * @return array
     */
    public function toSearchableArray(): array
    {
        return $this->makeHidden(['more', 'admin_user_id', 'deleted_at', 'is_import'])->toArray();
    }

    public function getThumbnailAttribute($value)
    {
        $awsUrl = config('filesystems.disks.s3.url');
        $pattern = '#^https?://[\w-]+(\.[\w-]+)+#i';
        $value = $awsUrl . preg_replace($pattern, '', $value);
        return (string) $value;
    }

    public function getMoreAttribute($value)
    {
        if (!$value) {
            return $value;
        }
        $awsUrl = config('filesystems.disks.s3.url');
        $datas = json_decode($value, true);
        $pattern = '#^https?://[\w-]+(\.[\w-]+)+#i';
        foreach ($datas as $key => &$data) {
            if (is_array($data)) {
                foreach ($data as &$dat) {
                    if (is_array($dat)) {
                        continue;
                    }
                    $dat =  preg_replace($pattern, $awsUrl, $dat);
                }
                continue;
            }
            $data = preg_replace($pattern, $awsUrl, $data);
        }

        if (isset($this->post_type) && $this->post_type == self::POST_TYPE_SECTION && !isset($datas['format'])) {
            //如果是切片，并且没有设置 $data['format'] 字段，则默认png
            $datas['format'] = 'png';
        }
        return $datas;
    }

    public function getPostContentAttribute($value)
    {
        return (string) $value;
    }

    public function getPostKeywordsAttribute($value)
    {
        $value = str_replace(',', ' ', $value);
        return str_replace('，', ' ', $value);
    }


    public function postRelated()
    {
        return $this->belongsToMany(Post::class, 'post_related', 'post_sn', 'post_sn_related', 'post_sn', 'post_sn');
    }

    public function categories()
    {
        return $this->belongsToMany(Category::class, 'category_post', 'post_id', 'category_code');
    }
}
