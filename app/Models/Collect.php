<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Collect extends Model
{
    use HasFactory;

    /**
     * 指定表名
     */
    protected $table = 'collect';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'name',
        'parent_id',
        'user_id',
        'post_id',
    ];

    /**
     * 需要转换为日期的属性
     */
    protected $dates = [
        'created_at',
        'updated_at'
    ];

    /**
     * 自定义日期格式
     */
    protected function serializeDate(\DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    /**
     * 与用户的关联关系
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 与资源的关联关系
     */
    public function post()
    {
        return $this->belongsTo(Post::class, 'post_id', 'id');
    }

    /**
     * 与父文件夹的关联关系
     */
    public function parent()
    {
        return $this->belongsTo(Collect::class, 'parent_id', 'id');
    }

    /**
     * 与子文件夹的关联关系
     */
    public function children()
    {
        return $this->hasMany(Collect::class, 'parent_id', 'id');
    }

    /**
     * 获取所有子文件夹（递归）
     */
    public function allChildren()
    {
        return $this->children()->with('allChildren');
    }

    /**
     * 检查用户是否已收藏某个资源
     */
    public static function isCollected($userId, $postId)
    {
        return self::where('user_id', $userId)
                   ->where('post_id', $postId)
                   ->whereNotNull('post_id')
                   ->exists();
    }

    /**
     * 获取用户收藏的资源数量（不包括文件夹）
     */
    public static function getCollectCount($userId)
    {
        return self::where('user_id', $userId)
                   ->whereNotNull('post_id')
                   ->count();
    }

    /**
     * 获取用户的文件夹数量
     */
    public static function getFolderCount($userId)
    {
        return self::where('user_id', $userId)
                   ->whereNull('post_id')
                   ->count();
    }

    /**
     * 检查是否为文件夹
     */
    public function isFolder()
    {
        return is_null($this->post_id);
    }

    /**
     * 检查是否为收藏资源
     */
    public function isResource()
    {
        return !is_null($this->post_id);
    }

    /**
     * 获取文件夹的完整路径
     */
    public function getFullPath()
    {
        $path = [];
        $current = $this;

        while ($current) {
            if ($current->name) {
                array_unshift($path, $current->name);
            }
            $current = $current->parent;
        }

        return implode(' / ', $path);
    }

    /**
     * 获取文件夹的层级深度
     */
    public function getDepth()
    {
        $depth = 0;
        $current = $this;

        while ($current->parent) {
            $depth++;
            $current = $current->parent;
        }

        return $depth;
    }
}
